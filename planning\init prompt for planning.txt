Please act as a senior product manager and help me to make a planning for PODToolsy web app that has all of the features below, please make it clear and more detail. All of these features are around Printify platform and its API: https://developers.printify.com/#overview
Here are the features:
1. Dashboard
2. Printify pricing manager with production cost, shipping fees, market fees, discount, retail price, profit margin, profit.
3. Media Library management (design images uploaded for products) such as bulk upload designs and manage
4. Customers management since Printify delete customers info such as their address and email in last 30 days, so we need to store those info for future promotion
5. Orders management (Auto import orders from Printify, also allow manually create orders includes shipping address validator using smarty API )
6. Products management (after user artwork has been added, they are referred to as products). Please also include those features:
	6.1. Products duplicator or copier with different designs images. 
	6.2. Publish  multiple products to multiple shops (etsy, shopify...) includes mock-up generater
7. Printify Catalog management (Products in the Printify catalog are referred to as blueprints (only after user artwork has been added, they are referred to as products))

Here are some additional important points:
- Every blueprint in the Printify catalog has multiple Print Providers that offer that blueprint. In addition to general differences between Print Providers including location and print technology employed, each Print Provider also offers different colors, sizes, print areas and prices.Each Print Provider's blueprint has specific option (e.g. color, size) combinations known as variants. Variants also contain information on a products' available print areas and sizes.

- The shipping endpoint API V1 has no shipping method data.The shipping endpoint API V2 has shipping method data. However, before calling endpoint for shipping methods such as standard, economy, priority,... we need to call this V2 shipping endpoint to get what are the shipping methods available for a specific product: /v2/catalog/blueprints/{{blueprint_id}}/print_providers/{{print_provider_id}}/shipping.json
And we get the response like this:
{
    "data": [
        {
            "type": "shipping_method",
            "id": "1",
            "attributes": {
                "name": "standard"
            }
        },
        {
            "type": "shipping_method",
            "id": "2",
            "attributes": {
                "name": "priority"
            }
        },
        {
            "type": "shipping_method",
            "id": "4",
            "attributes": {
                "name": "economy"
            }
        }
    ],
    "links": {
        "standard": "https://api.printify.com/v2/catalog/blueprints/478/print_providers/28/shipping/standard.json",
        "priority": "https://api.printify.com/v2/catalog/blueprints/478/print_providers/28/shipping/priority.json",
        "economy": "https://api.printify.com/v2/catalog/blueprints/478/print_providers/28/shipping/economy.json"
    }
}
So look at the last "links" attribute, we know this product has 3 shipping methods and also know the endpoint links to call in order to get the shipping info includes cost, country, and method. The disadvantage for V2 is that we need to call more endpoints than V1 but we get all the info we need.
If one shipping method is not available, here is the response:
{
    "data": []
}

- Also include the development phases planning follow the priority follow the numbered orders. Also suggest the pricing plans since this is a SaaS web app. Please make the pricing simple, clear, affordable, and transparent. I have attached the init supabase database, please suggest the complete database for this project.

- The 1 & 2 (Dashboard & Printify pricing manager) has been done and need to implement the rest.