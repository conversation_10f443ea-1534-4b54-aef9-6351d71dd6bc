
PODToolsy Web App – Full Planning Outline (v1.0)

Author: Senior Product Manager
Platform: SaaS web app built on Supabase + Printify API
Focus: Print-on-Demand automation, productivity, and profitability for Printify sellers

-----------------------------------------------
🏁 Current Status
-----------------------------------------------
✔️ Dashboard – DONE
✔️ Printify Pricing Manager – DONE
🔜 Remaining features below are prioritized by phase.

-----------------------------------------------
🚀 Phase 1 – Core Feature Implementation
-----------------------------------------------

1. Media Library (Design Upload Manager)
- Bulk upload images to Printify (privacy-respecting)
- No storage of image files — only track upload status
- Tagging, assignment to product blueprints
- Upload history dashboard

2. Customer Management
- Store address, email, and contact info for Printify orders
- Retain for future promotions/marketing
- Export support for CSV or integration with Mailchimp

3. Orders Management
- Auto import Printify orders
- Manual order creation
- Use Smarty API to validate addresses
- Track status: Draft, Submitted, In Production, Shipped, Delivered
- Link orders to stored customer info

-----------------------------------------------
🚀 Phase 2 – Product Management Tools
-----------------------------------------------

4. Products Management
- Duplicate products across designs (same blueprint/variants)
- Apply new design from upload manager
- Support multiple sales channels (Shopify, Etsy)
- Auto-publish to selected shops
- Use Printify mockup generator to create previews

-----------------------------------------------
🚀 Phase 3 – Catalog Management
-----------------------------------------------

5. Printify Catalog Management
- Browse & search all Printify blueprints
- Filter by provider, color, size, availability
- Load available shipping methods (via V2 shipping endpoint)
- Call V2 endpoint: /v2/catalog/blueprints/{id}/print_providers/{id}/shipping.json
- Use response "links" to fetch actual shipping costs
- Skip V1 shipping endpoint (no method data)

-----------------------------------------------
🔒 Design & Privacy Notes
-----------------------------------------------

- User image files are not stored or viewed.
- PODToolsy acts only as a proxy uploader to Printify.
- No Supabase Storage required for design files.

-----------------------------------------------
📦 Integrations
-----------------------------------------------

- Printify API (products, catalog, shipping, publishing)
- Shopify/Etsy integrations (future)
- Smarty (address validation) (future)
- Supabase (auth, database)

-----------------------------------------------
💸 Pricing Plan (SaaS)
-----------------------------------------------

| Plan       | Price | Features |
|------------|-------|----------|
| Free       | $0    | 20 products, 1 shop, limited features |
| Starter    | $12   | 200 products, 3 shops, bulk upload, customer tracking |
| Pro        | $29   | Unlimited products/shops, full features |
| Agency     | $79   | Teams, priority support, API access |

Transparent and affordable pricing. Billed monthly.

-----------------------------------------------
📅 Development Tools / Stack
-----------------------------------------------

- Frontend: React, Tailwind, Next.js (recommended)
- Backend: Supabase, Printify REST API
- Scheduler: Cron (for order sync)
- DB: Supabase PostgreSQL

