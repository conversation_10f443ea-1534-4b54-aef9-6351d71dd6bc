
-- PODToolsy Database Additions (v1.0)

-- Design Upload Manager (metadata only, no image files)
CREATE TABLE public.design_uploads (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  filename text NOT NULL,
  uploaded_to_printify boolean DEFAULT false,
  upload_status text,
  assigned_product_id uuid,
  uploaded_at timestamp with time zone DEFAULT now()
);

-- Customer Table
CREATE TABLE public.customers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  name text,
  email text,
  phone text,
  address_line1 text,
  address_line2 text,
  city text,
  state text,
  postal_code text,
  country text,
  created_at timestamp with time zone DEFAULT now()
);

-- Orders Table
CREATE TABLE public.orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  printify_order_id text,
  customer_id uuid REFERENCES public.customers(id),
  user_id uuid REFERENCES auth.users(id),
  status text,
  total_price numeric,
  shipping_address jsonb,
  created_at timestamp with time zone DEFAULT now()
);

-- Extension: Link product to design
ALTER TABLE tracked_products ADD COLUMN media_asset_id uuid REFERENCES public.design_uploads(id);
