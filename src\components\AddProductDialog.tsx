
import React, { useState } from "react";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface AddProductDialogProps {
  variant?: "header" | "section";
}

export const AddProductDialog = ({ variant = "section" }: AddProductDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({
    blueprint: "",
    provider: "",
    variant: "",
    productionCost: "",
    shippingCost: "",
    size: "",
    color: "",
    notes: "",
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // For now, just show a toast - this would integrate with Printify API
    toast({
      title: "Feature Coming Soon",
      description: "Add Product functionality will be integrated with Printify API",
    });
    
    setOpen(false);
    setFormData({
      blueprint: "",
      provider: "",
      variant: "",
      productionCost: "",
      shippingCost: "",
      size: "",
      color: "",
      notes: "",
    });
  };

  const isHeaderVariant = variant === "header";

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          size="sm" 
          variant={isHeaderVariant ? "outline" : "outline"}
          className={isHeaderVariant 
            ? "border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white" 
            : "border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
          }
        >
          <Plus className="h-4 w-4 mr-2" />
          {isHeaderVariant ? "" : "Add Product"}
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-gray-800 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle>Add Product to Track</DialogTitle>
          <DialogDescription className="text-gray-400">
            Add a product from the Printify catalog to track its pricing and profitability.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="blueprint" className="text-gray-300">Blueprint</Label>
              <Input
                id="blueprint"
                value={formData.blueprint}
                onChange={(e) => setFormData({...formData, blueprint: e.target.value})}
                placeholder="e.g. Matte Vertical Posters"
                className="bg-gray-700 border-gray-600 text-white"
                required
              />
            </div>
            <div>
              <Label htmlFor="provider" className="text-gray-300">Print Provider</Label>
              <Input
                id="provider"
                value={formData.provider}
                onChange={(e) => setFormData({...formData, provider: e.target.value})}
                placeholder="e.g. Printful"
                className="bg-gray-700 border-gray-600 text-white"
                required
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="variant" className="text-gray-300">Variant Title</Label>
            <Input
              id="variant"
              value={formData.variant}
              onChange={(e) => setFormData({...formData, variant: e.target.value})}
              placeholder="e.g. 11x14 Matte Poster"
              className="bg-gray-700 border-gray-600 text-white"
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="size" className="text-gray-300">Size</Label>
              <Input
                id="size"
                value={formData.size}
                onChange={(e) => setFormData({...formData, size: e.target.value})}
                placeholder="e.g. 11x14"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            <div>
              <Label htmlFor="color" className="text-gray-300">Color</Label>
              <Input
                id="color"
                value={formData.color}
                onChange={(e) => setFormData({...formData, color: e.target.value})}
                placeholder="e.g. White"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="productionCost" className="text-gray-300">Production Cost ($)</Label>
              <Input
                id="productionCost"
                type="number"
                step="0.01"
                value={formData.productionCost}
                onChange={(e) => setFormData({...formData, productionCost: e.target.value})}
                placeholder="0.00"
                className="bg-gray-700 border-gray-600 text-white"
                required
              />
            </div>
            <div>
              <Label htmlFor="shippingCost" className="text-gray-300">Shipping Cost ($)</Label>
              <Input
                id="shippingCost"
                type="number"
                step="0.01"
                value={formData.shippingCost}
                onChange={(e) => setFormData({...formData, shippingCost: e.target.value})}
                placeholder="0.00"
                className="bg-gray-700 border-gray-600 text-white"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes" className="text-gray-300">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              placeholder="Any additional notes about this product..."
              className="bg-gray-700 border-gray-600 text-white"
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Add Product
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
