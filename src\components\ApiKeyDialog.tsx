
import React, { useState, useEffect } from "react";
import { <PERSON>ting<PERSON>, <PERSON>, <PERSON>, <PERSON>Off, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";

interface ApiKeyDialogProps {
  variant?: "button" | "icon";
  triggerClassName?: string;
}

export const ApiKeyDialog = ({ variant = "button", triggerClassName }: ApiKeyDialogProps) => {
  const [open, setOpen] = useState(false);
  const [apiKey, setApiKey] = useState("");
  const [showApiKey, setShowApi<PERSON><PERSON>] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [hasExistingKey, setHasExistingKey] = useState(false);
  
  const { toast } = useToast();

  const loadExistingKey = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('user_api_keys')
        .select('api_key_encrypted')
        .eq('service_name', 'printify')
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setHasExistingKey(true);
        setApiKey(data.api_key_encrypted);
      }
    } catch (error) {
      console.error('Error loading API key:', error);
      toast({
        title: "Error",
        description: "Could not load existing API key.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      loadExistingKey();
    }
  }, [open]);

  const saveApiKey = async () => {
    if (!apiKey.trim()) {
      toast({
        title: "API Key Required",
        description: "Please enter your Printify API key.",
        variant: "destructive"
      });
      return;
    }

    setSaving(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('user_api_keys')
        .upsert({
          user_id: user.id,
          service_name: 'printify',
          api_key_encrypted: apiKey.trim()
        }, {
          onConflict: 'user_id,service_name'
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Printify API key saved successfully."
      });

      setHasExistingKey(true);
      setOpen(false);
    } catch (error) {
      console.error('Error saving API key:', error);
      toast({
        title: "Error",
        description: "Could not save API key. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const resetForm = () => {
    setApiKey("");
    setShowApiKey(false);
    setHasExistingKey(false);
  };

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen);
      if (!isOpen) resetForm();
    }}>
      <DialogTrigger asChild>
        {variant === "icon" ? (
          <Button size="sm" variant="outline" className={cn("border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white", triggerClassName)}>
            <Settings className="h-4 w-4" />
          </Button>
        ) : (
          <Button size="sm" variant="outline" className={cn("border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white", triggerClassName)}>
            <Key className="h-4 w-4 mr-2" />
            API Settings
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Printify API Configuration
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            {hasExistingKey 
              ? "Update your Printify API key to access the catalog."
              : "Enter your Printify API key to start searching products."
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2 text-gray-400">Loading...</span>
            </div>
          ) : (
            <>
              <div>
                <Label htmlFor="apiKey" className="text-gray-300">Printify API Key</Label>
                <div className="relative mt-1">
                  <Input
                    id="apiKey"
                    type={showApiKey ? "text" : "password"}
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="Enter your Printify API key..."
                    className="bg-gray-700 border-gray-600 text-white pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-600"
                  >
                    {showApiKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-gray-900 p-3 rounded border border-gray-600">
                <p className="text-sm text-gray-400 mb-2">
                  <strong>Where to find your API key:</strong>
                </p>
                <ol className="text-sm text-gray-400 space-y-1 list-decimal list-inside">
                  <li>Go to <a href="https://printify.com/app/account/api" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 underline">Printify API settings</a></li>
                  <li>Generate a new Personal Access Token</li>
                  <li>Copy and paste it here</li>
                </ol>
              </div>

              <div className="flex justify-end gap-2 pt-4 border-t border-gray-700">
                <Button 
                  variant="outline" 
                  onClick={() => setOpen(false)}
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={saveApiKey}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {saving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Key
                    </>
                  )}
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
