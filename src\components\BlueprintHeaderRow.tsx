import React, { useState } from "react";
import { ChevronDown, ChevronRight, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TrackedProduct } from "@/types/database";
import { BlueprintShippingControls } from "./BlueprintShippingControls";
import { ConfirmDialog } from "./ConfirmDialog";

interface BlueprintHeaderRowProps {
  blueprint: string;
  blueprintProducts: TrackedProduct[];
  isCollapsed: boolean;
  allInBlueprintSelected: boolean;
  updateMultipleProductsPending: boolean;
  deleteMultipleProductsPending: boolean;
  onToggleCollapse: (blueprint: string) => void;
  onBlueprintSelectionToggle: (blueprintProducts: TrackedProduct[]) => void;
  onDeleteBlueprintProducts: (
    blueprint: string,
    blueprintProducts: TrackedProduct[]
  ) => void;
  onBlueprintFreeShippingToggle: (
    blueprintProducts: TrackedProduct[],
    isFreeShipping: boolean
  ) => void;
  isActiveBlueprint?: boolean;
  isBlocked?: boolean;
}

export const BlueprintHeaderRow = ({
  blueprint,
  blueprintProducts,
  isCollapsed,
  allInBlueprintSelected,
  deleteMultipleProductsPending,
  onToggleCollapse,
  onBlueprintSelectionToggle,
  onDeleteBlueprintProducts,
  onBlueprintFreeShippingToggle,
  isActiveBlueprint = false,
  isBlocked = false,
}: BlueprintHeaderRowProps) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const getRowClasses = () => {
    const baseClasses = "transition-colors border-b border-gray-600";

    if (isActiveBlueprint) {
      return `${baseClasses} bg-blue-700/90 hover:bg-blue-700`;
    } else if (isBlocked) {
      return `${baseClasses} bg-gray-800/50 hover:bg-gray-800/70 opacity-60`;
    } else {
      return `${baseClasses} bg-gray-700/80 hover:bg-gray-700`;
    }
  };

  const allProductsOfferFreeShipping = blueprintProducts.every(
    (p) => p.offers_free_shipping
  );

  return (
    <tr className={getRowClasses()}>
      <td className="p-4">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={allInBlueprintSelected}
            onCheckedChange={() =>
              onBlueprintSelectionToggle(blueprintProducts)
            }
            disabled={isBlocked}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleCollapse(blueprint)}
            className="text-gray-200 hover:text-white p-0 h-auto hover:bg-transparent"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </td>
      <td className="p-4 text-white font-bold text-base" colSpan={9}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span
              className={isActiveBlueprint ? "text-blue-100" : "text-blue-200"}
            >
              {blueprint}
            </span>
            <span className="text-sm text-gray-300 font-normal">
              ({blueprintProducts.length} variants)
            </span>
            {isActiveBlueprint && (
              <span className="text-xs bg-blue-600 text-blue-100 px-2 py-1 rounded-full font-medium">
                Active
              </span>
            )}
            {isBlocked && (
              <span className="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded-full font-medium">
                Blocked
              </span>
            )}
          </div>

          {/* Shipping Controls Group - Right aligned */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3 bg-gray-800/50 rounded-lg px-3 py-2 border border-gray-600/50">
              {/* Free Shipping Checkbox */}
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={allProductsOfferFreeShipping}
                  onCheckedChange={(checked) =>
                    onBlueprintFreeShippingToggle(blueprintProducts, !!checked)
                  }
                  disabled={isBlocked}
                  className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                />
                <span className="text-xs text-gray-300 whitespace-nowrap font-medium">
                  Free Shipping
                </span>
              </div>

              {/* Divider */}
              <div className="w-px h-6 bg-gray-600"></div>

              {/* Country and Method Dropdowns */}
              <BlueprintShippingControls
                blueprintProducts={blueprintProducts}
                isBlocked={isBlocked}
              />
            </div>
          </div>
        </div>
      </td>
      <td className="p-4 align-middle">
        <Button
          size="sm"
          variant="destructive"
          onClick={() => setShowDeleteConfirm(true)}
          disabled={deleteMultipleProductsPending || isBlocked}
          className="bg-red-600 hover:bg-red-700 transition-colors"
        >
          <Trash2 className="h-4 w-4" />
        </Button>

        <ConfirmDialog
          open={showDeleteConfirm}
          onOpenChange={setShowDeleteConfirm}
          onConfirm={() =>
            onDeleteBlueprintProducts(blueprint, blueprintProducts)
          }
          title="Delete Product and Its Variants"
          description="Are you sure you want to delete this product and all of its variants? This action cannot be undone."
          confirmText="Delete All"
          isLoading={deleteMultipleProductsPending}
        />
      </td>
    </tr>
  );
};
