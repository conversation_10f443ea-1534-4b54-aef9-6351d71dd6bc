import React, { useEffect, useState, useCallback } from "react";
import { TrackedProduct } from "@/types/database";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useUpdateProductShipping,
  useCountries,
  useShippingMethods,
  useVariantShippingCosts,
} from "@/hooks/useShipping";
import { useToast } from "@/hooks/use-toast";

interface BlueprintShippingControlsProps {
  blueprintProducts: TrackedProduct[];
  isBlocked?: boolean;
}

export const BlueprintShippingControls = ({
  blueprintProducts,
  isBlocked = false,
}: BlueprintShippingControlsProps) => {
  const { toast } = useToast();
  const updateProductShipping = useUpdateProductShipping();
  const { data: countries = [] } = useCountries();
  const { data: shippingMethods = [] } = useShippingMethods();

  // Get the current shipping settings from the first product (they should all be the same for the blueprint)
  const firstProduct = blueprintProducts[0];
  const [currentCountryId, setCurrentCountryId] = useState<string | undefined>(
    firstProduct?.default_shipping_country_id
  );
  const [currentShippingMethodId, setCurrentShippingMethodId] = useState<
    string | undefined
  >(firstProduct?.default_shipping_method_id);

  // Get shipping costs for the first product to determine available options
  const { data: shippingCosts = [] } = useVariantShippingCosts(
    firstProduct?.id
  );

  // Get available countries and methods from actual shipping data
  const availableCountries = React.useMemo(() => {
    const countryIds = [
      ...new Set(shippingCosts.map((cost) => cost.country_id)),
    ];
    return countries.filter((country) => countryIds.includes(country.id));
  }, [shippingCosts, countries]);

  const availableShippingMethods = React.useMemo(() => {
    // If no country is selected, show all available methods
    if (!currentCountryId) {
      const methodIds = [
        ...new Set(shippingCosts.map((cost) => cost.shipping_method_id)),
      ];
      return shippingMethods.filter((method) => methodIds.includes(method.id));
    }

    // Filter methods based on selected country
    const methodIds = [
      ...new Set(
        shippingCosts
          .filter((cost) => cost.country_id === currentCountryId)
          .map((cost) => cost.shipping_method_id)
      ),
    ];
    return shippingMethods.filter((method) => methodIds.includes(method.id));
  }, [shippingCosts, shippingMethods, currentCountryId]);

  // Set default values when data is loaded and update database
  useEffect(() => {
    if (
      blueprintProducts.length > 0 &&
      availableCountries.length > 0 &&
      availableShippingMethods.length > 0
    ) {
      let needsUpdate = false;
      let defaultCountryId = currentCountryId;
      let defaultMethodId = currentShippingMethodId;

      // Set default country to US if none selected and available
      if (!defaultCountryId) {
        const usCountry = availableCountries.find((c) => c.code === "US");
        if (usCountry) {
          defaultCountryId = usCountry.id;
          setCurrentCountryId(usCountry.id);
          needsUpdate = true;
        } else if (availableCountries.length > 0) {
          // Fallback to first available country
          defaultCountryId = availableCountries[0].id;
          setCurrentCountryId(availableCountries[0].id);
          needsUpdate = true;
        }
      }

      // Set default shipping method to Standard if none selected and available
      if (!defaultMethodId) {
        const standardMethod = availableShippingMethods.find(
          (sm) => sm.name === "Standard"
        );
        if (standardMethod) {
          defaultMethodId = standardMethod.id;
          setCurrentShippingMethodId(standardMethod.id);
          needsUpdate = true;
        } else if (availableShippingMethods.length > 0) {
          // Fallback to first available method
          defaultMethodId = availableShippingMethods[0].id;
          setCurrentShippingMethodId(availableShippingMethods[0].id);
          needsUpdate = true;
        }
      }

      // Update database with defaults if needed
      if (needsUpdate && defaultCountryId && defaultMethodId) {
        handleShippingUpdate(defaultCountryId, defaultMethodId);
      }
    }
  }, [availableCountries, availableShippingMethods, blueprintProducts.length]);

  // Effect to handle shipping method validation when country changes
  React.useEffect(() => {
    if (
      currentCountryId &&
      currentShippingMethodId &&
      availableShippingMethods.length > 0
    ) {
      // Check if current method is still available for the selected country
      const isMethodAvailable = availableShippingMethods.some(
        (method) => method.id === currentShippingMethodId
      );

      if (!isMethodAvailable) {
        // Current method is not available for this country, select first available
        const firstAvailableMethod = availableShippingMethods[0];
        if (firstAvailableMethod) {
          setCurrentShippingMethodId(firstAvailableMethod.id);
          handleShippingUpdate(currentCountryId, firstAvailableMethod.id);
        }
      }
    }
  }, [currentCountryId, availableShippingMethods, currentShippingMethodId]);

  const handleShippingUpdate = async (
    countryId?: string,
    shippingMethodId?: string
  ) => {
    if (!countryId || !shippingMethodId) return;

    try {
      // Update all products in this blueprint with the new shipping settings
      const updatePromises = blueprintProducts.map((product) =>
        updateProductShipping.mutateAsync({
          productId: product.id,
          countryId,
          shippingMethodId,
        })
      );

      await Promise.all(updatePromises);

      toast({
        title: "Success",
        description: "Shipping settings updated for all variants.",
      });
    } catch (error) {
      console.error("Error updating shipping:", error);
      toast({
        title: "Error",
        description: "Failed to update shipping settings.",
        variant: "destructive",
      });
    }
  };

  const handleCountryChange = (countryId: string) => {
    setCurrentCountryId(countryId);

    // Find available methods for the new country
    const availableMethodsForCountry = shippingCosts
      .filter((cost) => cost.country_id === countryId)
      .map((cost) => cost.shipping_method_id);

    const availableMethods = shippingMethods.filter((method) =>
      availableMethodsForCountry.includes(method.id)
    );

    // Reset shipping method to first available for this country
    if (availableMethods.length > 0) {
      const firstMethodId = availableMethods[0].id;
      setCurrentShippingMethodId(firstMethodId);
      handleShippingUpdate(countryId, firstMethodId);
    } else {
      // No methods available for this country, clear the method
      setCurrentShippingMethodId(undefined);
    }
  };

  const handleShippingMethodChange = (methodId: string) => {
    setCurrentShippingMethodId(methodId);
    handleShippingUpdate(currentCountryId, methodId);
  };

  return (
    <div className="flex items-center gap-3 text-xs">
      {/* Country Dropdown */}
      <div className="flex items-center gap-2">
        <span className="text-xs text-gray-300 whitespace-nowrap font-medium">
          Country:
        </span>
        <div className="min-w-[90px]">
          <Select
            value={currentCountryId}
            onValueChange={handleCountryChange}
            disabled={isBlocked || updateProductShipping.isPending}
          >
            <SelectTrigger className="w-full h-7 text-xs bg-gray-700 border border-gray-600 hover:bg-gray-600 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border border-gray-600 shadow-xl z-50">
              {availableCountries.map((country) => (
                <SelectItem
                  key={country.id}
                  value={country.id}
                  className="text-xs text-gray-200 hover:bg-gray-700 focus:bg-gray-700"
                >
                  <span className="text-gray-200">{country.name}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Method Dropdown */}
      <div className="flex items-center gap-2">
        <span className="text-xs text-gray-300 whitespace-nowrap font-medium">
          Method:
        </span>
        <div className="min-w-[100px]">
          <Select
            value={currentShippingMethodId}
            onValueChange={handleShippingMethodChange}
            disabled={isBlocked || updateProductShipping.isPending}
          >
            <SelectTrigger className="w-full h-7 text-xs bg-gray-700 border border-gray-600 hover:bg-gray-600 focus:ring-blue-500 focus:border-blue-500">
              <SelectValue placeholder="Select..." />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 border border-gray-600 shadow-xl z-50">
              {availableShippingMethods.map((method) => (
                <SelectItem
                  key={method.id}
                  value={method.id}
                  className="text-xs text-gray-200 hover:bg-gray-700 focus:bg-gray-700"
                >
                  {method.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};
