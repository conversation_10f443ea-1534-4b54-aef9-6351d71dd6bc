
import React, { useState } from "react";
import { <PERSON>culator, Loader2, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface Store {
  id: number;
  title: string;
  platform: string;
}

interface Variant {
  id: number;
  title: string;
  options: {
    color?: string;
    size?: string;
  };
}

interface CostCalculatorDialogProps {
  blueprintId: number;
  printProviderId: number;
  variantIds: number[];
  onCostsCalculated: (costs: { variantId: number; productionCost: number; shippingCost: number }[]) => void;
}

export const CostCalculatorDialog = ({ 
  blueprintId, 
  printProviderId, 
  variantIds, 
  onCostsCalculated 
}: CostCalculatorDialogProps) => {
  const [open, setOpen] = useState(false);
  const [stores, setStores] = useState<Store[]>([]);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [isLoadingStores, setIsLoadingStores] = useState(false);
  const [isGettingCosts, setIsGettingCosts] = useState(false);
  const [createdProductId, setCreatedProductId] = useState<string | null>(null);
  
  const { toast } = useToast();

  const groupVariantsBySize = (variants: Variant[]) => {
    const sizeGroups: Record<string, Variant[]> = {};
    
    variants.forEach(variant => {
      const size = variant.options?.size || 'default';
      if (!sizeGroups[size]) {
        sizeGroups[size] = [];
      }
      sizeGroups[size].push(variant);
    });
    
    return sizeGroups;
  };

  const selectRepresentativeVariants = (sizeGroups: Record<string, Variant[]>) => {
    const representatives: Variant[] = [];
    
    Object.values(sizeGroups).forEach(sizeVariants => {
      representatives.push(sizeVariants[0]);
    });
    
    return representatives;
  };

  const applyCostsToAllVariants = (
    allVariants: Variant[], 
    representativeCosts: any[], 
    sizeGroups: Record<string, Variant[]>
  ) => {
    const allCosts: any[] = [];
    
    const sizeCostMap: Record<string, { productionCost: number; shippingCost: number }> = {};
    
    representativeCosts.forEach(costData => {
      for (const [size, variants] of Object.entries(sizeGroups)) {
        if (variants.some(v => v.id === costData.variantId)) {
          sizeCostMap[size] = {
            productionCost: costData.productionCost,
            shippingCost: costData.shippingCost
          };
          break;
        }
      }
    });
    
    allVariants.forEach(variant => {
      const size = variant.options?.size || 'default';
      const costData = sizeCostMap[size];
      
      if (costData) {
        allCosts.push({
          variantId: variant.id,
          productionCost: costData.productionCost,
          shippingCost: costData.shippingCost
        });
      } else {
        allCosts.push({
          variantId: variant.id,
          productionCost: 0,
          shippingCost: 0
        });
      }
    });
    
    return allCosts;
  };

  const loadStores = async () => {
    setIsLoadingStores(true);
    try {
      const response = await supabase.functions.invoke('printify-catalog', {
        body: { action: 'get-stores' }
      });
      
      if (response.error) throw new Error(response.error.message || 'Failed to load stores');
      setStores(response.data || []);
    } catch (error) {
      console.error('Error loading stores:', error);
      toast({
        title: "Error",
        description: error.message || "Could not load Printify stores.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingStores(false);
    }
  };

  const getCosts = async () => {
    if (!selectedStore) return;

    setIsGettingCosts(true);
    try {
      // Get variant details to group by size
      const variantsResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'get-blueprint-variants',
          blueprintId,
          printProviderId
        }
      });
      
      if (variantsResponse.error) {
        throw new Error('Failed to get variants for grouping');
      }

      const allVariantsData = variantsResponse.data?.variants || [];
      const requestedVariants = allVariantsData.filter((v: Variant) => variantIds.includes(v.id));
      
      console.log(`Processing ${requestedVariants.length} variants for cost retrieval`);
      
      // Group variants by size and select representatives
      const sizeGroups = groupVariantsBySize(requestedVariants);
      const representativeVariants = selectRepresentativeVariants(sizeGroups);
      const representativeVariantIds = representativeVariants.map(v => v.id);
      
      console.log(`Using ${representativeVariants.length} size representatives out of ${requestedVariants.length} total variants`);

      // Create placeholder product with representative variants only
      const createResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'create-placeholder-product',
          storeId: selectedStore.id,
          blueprintId,
          printProviderId,
          variantIds: representativeVariantIds
        }
      });
      
      if (createResponse.error) throw new Error(createResponse.error.message || 'Failed to create product');
      
      const productId = createResponse.data?.id;
      if (!productId) throw new Error('No product ID returned');
      
      setCreatedProductId(productId);

      // Get production costs for representatives
      const costsResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'get-product-costs',
          storeId: selectedStore.id,
          productId
        }
      });
      
      if (costsResponse.error) throw new Error(costsResponse.error.message || 'Failed to get costs');
      
      const productData = costsResponse.data;
      
      const representativeCosts = productData.variants
        ?.filter((variant: any) => representativeVariantIds.includes(variant.id))
        .map((variant: any) => ({
          variantId: variant.id,
          productionCost: variant.cost || 0,
          shippingCost: 0
        })) || [];

      // Apply costs to all variants based on size grouping
      const allCosts = applyCostsToAllVariants(requestedVariants, representativeCosts, sizeGroups);

      console.log(`Applied costs to all ${allCosts.length} variants based on size grouping`);
      onCostsCalculated(allCosts);

      toast({
        title: "Success",
        description: `Retrieved production costs for ${allCosts.length} variants using ${representativeVariants.length} size representatives.`
      });

      setOpen(false);

    } catch (error) {
      console.error('Error getting costs:', error);
      toast({
        title: "Error",
        description: error.message || "Could not get costs from Printify.",
        variant: "destructive"
      });
    } finally {
      setIsGettingCosts(false);
      
      // Clean up placeholder product
      if (createdProductId && selectedStore) {
        try {
          await supabase.functions.invoke('printify-catalog', {
            body: { 
              action: 'delete-placeholder-product',
              storeId: selectedStore.id,
              productId: createdProductId
            }
          });
        } catch (cleanupError) {
          console.warn('Could not clean up placeholder product:', cleanupError);
        }
      }
      setCreatedProductId(null);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen && stores.length === 0) {
      loadStores();
    }
    if (!isOpen) {
      setSelectedStore(null);
      setCreatedProductId(null);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
          <Calculator className="h-4 w-4 mr-2" />
          Get Real Costs
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-gray-800 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle>Get Production Costs</DialogTitle>
          <DialogDescription className="text-gray-400">
            This will retrieve accurate production costs for all {variantIds.length} selected variants from Printify. We'll create one temporary product with representative variants from each size, then apply those costs to all variants of the same size.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-4">
          <div>
            <label className="text-sm font-medium text-gray-300 mb-2 block">
              Select Printify Store
            </label>
            {isLoadingStores ? (
              <div className="flex items-center gap-2 p-3 bg-gray-700 rounded">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-gray-400">Loading stores...</span>
              </div>
            ) : (
              <Select onValueChange={(value) => {
                const store = stores.find(s => s.id.toString() === value);
                setSelectedStore(store || null);
              }}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Choose a store to get costs from" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  {stores.map((store) => (
                    <SelectItem key={store.id} value={store.id.toString()}>
                      {store.title} ({store.platform})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="bg-blue-600/20 border border-blue-500/30 rounded p-3 text-sm text-blue-200">
            <strong>Smart Cost Retrieval:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Groups variants by size (colors typically have same cost per size)</li>
              <li>Creates temporary product with one variant per size group</li>
              <li>Retrieves costs from Printify for each size</li>
              <li>Applies retrieved costs to all variants of the same size</li>
              <li>Handles any number of variants efficiently (even 400+)</li>
              <li>Automatically deletes temporary product after retrieval</li>
            </ol>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button 
              onClick={getCosts}
              disabled={!selectedStore || isGettingCosts}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isGettingCosts ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Getting Costs...
                </>
              ) : (
                <>
                  <Calculator className="h-4 w-4 mr-2" />
                  Get Costs (Size-Based)
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
