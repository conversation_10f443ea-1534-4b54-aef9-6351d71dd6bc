import React, { useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCountries } from "@/hooks/useShipping";
import { Country } from "@/types/database";

interface CountrySelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  defaultCountry?: string;
}

export const CountrySelect = ({
  value,
  onValueChange,
  disabled,
  defaultCountry = "US",
}: CountrySelectProps) => {
  const { data: allCountries = [], isLoading } = useCountries();

  // Filter to only show commonly available countries for print-on-demand
  const availableCountries = allCountries.filter((country) =>
    [
      "US",
      "CA",
      "GB",
      "AU",
      "DE",
      "FR",
      "IT",
      "ES",
      "NL",
      "BE",
      "SE",
      "DK",
      "NO",
      "FI",
      "PL",
      "CZ",
      "AT",
      "CH",
      "IE",
      "PT",
      "JP",
      "NZ",
    ].includes(country.code)
  );

  const selectedCountry = availableCountries.find((c) => c.id === value);

  // Set default value if none is selected and we have countries loaded
  useEffect(() => {
    if (!value && availableCountries.length > 0 && !isLoading) {
      const defaultCountryData = availableCountries.find(
        (c) => c.code === defaultCountry
      );
      if (defaultCountryData) {
        onValueChange(defaultCountryData.id);
      }
    }
  }, [value, availableCountries, isLoading, defaultCountry, onValueChange]);

  return (
    <Select
      value={value}
      onValueChange={onValueChange}
      disabled={disabled || isLoading}
    >
      <SelectTrigger className="w-32 h-8 text-xs bg-background border border-border">
        <SelectValue
          placeholder={
            isLoading ? "Loading..." : selectedCountry?.name || "Select country"
          }
        />
      </SelectTrigger>
      <SelectContent className="bg-background border border-border shadow-lg z-50">
        {availableCountries.map((country) => (
          <SelectItem key={country.id} value={country.id} className="text-xs">
            <span>{country.name}</span>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
