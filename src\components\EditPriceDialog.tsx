
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useUpdateMultipleTrackedProducts } from '@/hooks/useTrackedProducts';
import { useToast } from '@/hooks/use-toast';
import { Market, TrackedProduct } from '@/types/database';
import { calculateProfit, calculatePriceFromProfit, calculatePriceFromMargin } from '@/utils/feeCalculator';
import { Edit, DollarSign, TrendingUp, Percent } from 'lucide-react';

interface EditPriceDialogProps {
  selectedProducts: TrackedProduct[];
  market: Market | undefined;
  onUpdate: () => void;
}

type EditMode = 'price' | 'profit' | 'margin';

export const EditPriceDialog = ({ selectedProducts, market, onUpdate }: EditPriceDialogProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editMode, setEditMode] = useState<EditMode>('price');
  const [price, setPrice] = useState('');
  const [profit, setProfit] = useState('');
  const [margin, setMargin] = useState('');
  
  const updateProducts = useUpdateMultipleTrackedProducts();
  const { toast } = useToast();

  const disabled = selectedProducts.length === 0 || !market;

  // Get the blueprint name for selected products
  const getSelectedBlueprint = () => {
    if (selectedProducts.length === 0) return null;
    return selectedProducts[0]?.blueprints?.title || 'Unknown Product';
  };

  // Validate that all selected products are from the same blueprint
  const validateSameBlueprint = () => {
    if (selectedProducts.length === 0) return true;
    const firstBlueprint = selectedProducts[0]?.blueprints?.title;
    return selectedProducts.every(p => p.blueprints?.title === firstBlueprint);
  };

  const handleApply = async () => {
    if (!market) {
      toast({ title: 'Error', description: 'Market data not available.', variant: 'destructive' });
      return;
    }

    if (!validateSameBlueprint()) {
      toast({ 
        title: 'Invalid Selection', 
        description: 'All selected variants must be from the same product.', 
        variant: 'destructive' 
      });
      return;
    }

    let updates: { id: string; data: Partial<Omit<TrackedProduct, 'id'>> }[] = [];

    if (editMode === 'price') {
      const newPrice = parseFloat(price);
      if (isNaN(newPrice) || newPrice < 0) {
          toast({ title: 'Invalid price', description: 'Please enter a valid positive number.', variant: 'destructive' });
          return;
      }
      updates = selectedProducts.map(product => {
        const newProfit = calculateProfit({ ...product, retail_price: newPrice }, market);
        const newMargin = newPrice > 0 ? (newProfit / newPrice) * 100 : 0;
        return {
          id: product.id,
          data: { 
            retail_price: newPrice,
            profit_margin_percentage: Math.round(newMargin)
          }
        };
      });
    } else if (editMode === 'profit') {
      const newProfit = parseFloat(profit);
      if (isNaN(newProfit)) {
        toast({ title: 'Invalid profit', description: 'Please enter a valid number.', variant: 'destructive' });
        return;
      }

      updates = selectedProducts.map(product => {
        if (!market) return { id: product.id, data: {} };
        
        const newPrice = calculatePriceFromProfit(newProfit, product, market);

        if (newPrice === null || newPrice < 0) {
           return { id: product.id, data: {} };
        }
        
        const newMargin = newPrice > 0 ? (newProfit / newPrice) * 100 : 0;

        return {
          id: product.id,
          data: { 
            retail_price: parseFloat(newPrice.toFixed(2)),
            profit_margin_percentage: Math.round(newMargin)
          }
        };
      });
      
      const validUpdates = updates.filter(u => Object.keys(u.data).length > 0);
      if (validUpdates.length !== selectedProducts.length) {
        toast({ title: 'Calculation Error', description: 'Could not calculate a valid price for some products with the given profit.', variant: 'destructive' });
        return;
      }
      updates = validUpdates;

    } else if (editMode === 'margin') {
      const newMarginPercent = parseFloat(margin);
      if (isNaN(newMarginPercent)) {
          toast({ title: 'Invalid margin', description: 'Please enter a valid number.', variant: 'destructive' });
          return;
      }

      updates = selectedProducts.map(product => {
        if (!market) return { id: product.id, data: {} };

        const newPrice = calculatePriceFromMargin(newMarginPercent, product, market);
        
        if (newPrice === null || newPrice < 0) {
           return { id: product.id, data: {} };
        }
        
        return {
          id: product.id,
          data: { 
            retail_price: parseFloat(newPrice.toFixed(2)),
            profit_margin_percentage: Math.round(newMarginPercent)
          }
        };
      });
      
      const validUpdates = updates.filter(u => Object.keys(u.data).length > 0);
      if (validUpdates.length !== selectedProducts.length) {
          toast({ title: 'Invalid margin', description: 'The specified profit margin is too high with current market fees for some products.', variant: 'destructive' });
          return;
      }
      updates = validUpdates;
    }
    
    if (updates.length === 0) {
        toast({ title: 'No changes', description: 'No changes to apply.', variant: 'default' });
        return;
    }

    try {
        await updateProducts.mutateAsync(updates);
        toast({ title: 'Success', description: `${selectedProducts.length} variants updated.` });
        setIsOpen(false);
        onUpdate();
        setPrice('');
        setProfit('');
        setMargin('');
    } catch (error) {
        toast({ title: 'Error', description: 'Failed to update products.', variant: 'destructive' });
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
        setPrice('');
        setProfit('');
        setMargin('');
    }
    setIsOpen(open);
  }

  const selectedBlueprint = getSelectedBlueprint();

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          disabled={disabled}
          className="h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground transition-colors"
        >
          <Edit className="w-4 h-4 mr-2" />
          Update Pricing ({selectedProducts.length})
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[480px] bg-card border-border">
        <DialogHeader className="space-y-3">
          <DialogTitle className="text-xl font-semibold text-foreground flex items-center gap-2">
            <Edit className="w-5 h-5 text-primary" />
            Update Product Pricing
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {selectedBlueprint && (
              <div className="mb-3 p-3 bg-muted/50 rounded-lg border">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-foreground">Product:</span> 
                  <span className="text-primary font-medium">{selectedBlueprint}</span>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  Updating {selectedProducts.length} variant{selectedProducts.length === 1 ? '' : 's'}
                </div>
              </div>
            )}
            Choose how you want to update the pricing for the selected variants.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Tabs value={editMode} onValueChange={(value) => setEditMode(value as EditMode)} className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-muted">
              <TabsTrigger value="price" className="flex items-center gap-2">
                <DollarSign className="w-4 h-4" />
                Price
              </TabsTrigger>
              <TabsTrigger value="profit" className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Profit
              </TabsTrigger>
              <TabsTrigger value="margin" className="flex items-center gap-2">
                <Percent className="w-4 h-4" />
                Margin
              </TabsTrigger>
            </TabsList>
            
            <div className="mt-6">
              <TabsContent value="price" className="space-y-4 mt-0">
                <div className="space-y-2">
                  <Label htmlFor="price" className="text-sm font-medium text-foreground">
                    Set retail price for all variants
                  </Label>
                  <div className="relative">
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground font-medium">
                      $
                    </span>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={price}
                      onChange={(e) => setPrice(e.target.value)}
                      placeholder="29.99"
                      className="pl-8 bg-background border-input"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Sets the same retail price for all selected variants
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="profit" className="space-y-4 mt-0">
                <div className="space-y-2">
                  <Label htmlFor="profit" className="text-sm font-medium text-foreground">
                    Set target profit for all variants
                  </Label>
                  <div className="relative">
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-muted-foreground font-medium">
                      $
                    </span>
                    <Input
                      id="profit"
                      type="number"
                      step="0.01"
                      value={profit}
                      onChange={(e) => setProfit(e.target.value)}
                      placeholder="8.50"
                      className="pl-8 bg-background border-input"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Calculates retail price to achieve this profit for each variant
                  </p>
                </div>
              </TabsContent>
              
              <TabsContent value="margin" className="space-y-4 mt-0">
                <div className="space-y-2">
                  <Label htmlFor="margin" className="text-sm font-medium text-foreground">
                    Set target profit margin for all variants
                  </Label>
                  <div className="relative">
                    <Input
                      id="margin"
                      type="number"
                      step="1"
                      value={margin}
                      onChange={(e) => setMargin(e.target.value)}
                      placeholder="35"
                      className="pr-8 bg-background border-input"
                    />
                    <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground font-medium">
                      %
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Calculates retail price to achieve this profit margin for each variant
                  </p>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
        
        <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 pt-6 border-t border-border">
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleApply} 
            disabled={updateProducts.isPending || disabled}
            className="w-full sm:w-auto bg-primary hover:bg-primary/90"
          >
            {updateProducts.isPending ? 'Updating...' : 'Apply Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
