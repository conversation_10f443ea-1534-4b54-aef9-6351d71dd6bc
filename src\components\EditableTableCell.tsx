import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";

interface EditableTableCellProps {
  initialValue: number | string;
  onSave: (newValue: number) => void;
  prefix?: string;
  suffix?: string;
  className?: string;
  disabled?: boolean;
}

export const EditableTableCell = ({
  initialValue,
  onSave,
  prefix,
  suffix,
  className,
  disabled = false,
}: EditableTableCellProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(String(initialValue));

  useEffect(() => {
    setValue(String(initialValue));
  }, [initialValue]);

  const handleSave = () => {
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue !== Number(initialValue)) {
      onSave(numericValue);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      setValue(String(initialValue));
      setIsEditing(false);
    }
  };

  const handleCellClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  if (isEditing) {
    return (
      <td className={`p-2 align-middle ${className}`}>
        <div className="relative w-20">
          {prefix && (
            <span className="absolute inset-y-0 left-0 flex items-center pl-1 text-muted-foreground text-xs z-10">
              {prefix}
            </span>
          )}
          <Input
            type="text"
            inputMode="decimal"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyDown}
            autoFocus
            className={`h-7 text-xs bg-background border-border focus-visible:ring-1 focus-visible:ring-ring focus-visible:border-ring text-center w-full ${
              prefix ? "pl-4" : ""
            } ${suffix ? "pr-4" : ""}`}
            style={{
              WebkitAppearance: "none",
              MozAppearance: "textfield",
            }}
          />
          {suffix && (
            <span className="absolute inset-y-0 right-0 flex items-center pr-1 text-muted-foreground text-xs z-10">
              {suffix}
            </span>
          )}
        </div>
      </td>
    );
  }

  const displayValue =
    typeof initialValue === "number" ? initialValue.toFixed(2) : initialValue;

  return (
    <td
      onClick={handleCellClick}
      className={`p-4 align-middle ${
        disabled ? "cursor-not-allowed" : "cursor-pointer hover:bg-gray-700/50"
      } ${className}`}
    >
      {prefix}
      {displayValue}
      {suffix}
    </td>
  );
};
