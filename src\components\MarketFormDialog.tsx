import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreateMarket, useUpdateMarket } from '@/hooks/useMarkets';
import { Market } from '@/types/database';
import { useToast } from "@/hooks/use-toast";

const feeTypeEnum = z.enum(['percentage', 'flat']);

const marketSchema = z.object({
  name: z.string().min(1, 'Market name is required'),
  transaction_fee: z.coerce.number().min(0, 'Fee must be non-negative'),
  transaction_fee_type: feeTypeEnum,
  processing_fee: z.coerce.number().min(0, 'Fee must be non-negative'),
  processing_fee_type: feeTypeEnum,
  other_fee: z.coerce.number().min(0, 'Fee must be non-negative'),
  other_fee_type: feeTypeEnum,
});

type MarketFormValues = z.infer<typeof marketSchema>;

interface MarketFormDialogProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  market: Market | null;
}

export const MarketFormDialog = ({ isOpen, setIsOpen, market }: MarketFormDialogProps) => {
  const { toast } = useToast();
  const createMarket = useCreateMarket();
  const updateMarket = useUpdateMarket();

  const form = useForm<MarketFormValues>({
    resolver: zodResolver(marketSchema),
    defaultValues: {
      name: '',
      transaction_fee: 0,
      transaction_fee_type: 'percentage',
      processing_fee: 0,
      processing_fee_type: 'percentage',
      other_fee: 0,
      other_fee_type: 'flat',
    }
  });

  useEffect(() => {
    if (market) {
      form.reset({
        name: market.name,
        transaction_fee: market.transaction_fee,
        transaction_fee_type: market.transaction_fee_type,
        processing_fee: market.processing_fee,
        processing_fee_type: market.processing_fee_type,
        other_fee: market.other_fee,
        other_fee_type: market.other_fee_type,
      });
    } else {
      form.reset({
        name: '',
        transaction_fee: 0,
        transaction_fee_type: 'percentage',
        processing_fee: 0,
        processing_fee_type: 'percentage',
        other_fee: 0,
        other_fee_type: 'flat',
      });
    }
  }, [market, form, isOpen]);

  const onSubmit = (values: MarketFormValues) => {
    const commonCallbacks = {
        onSuccess: () => {
            toast({
              title: `Market ${market ? 'updated' : 'created'}`,
              description: `The market has been successfully ${market ? 'updated' : 'created'}.`,
            });
            setIsOpen(false);
          },
          onError: (error: Error) => {
            toast({
              title: `Error ${market ? 'updating' : 'creating'} market`,
              description: error.message,
              variant: "destructive" as const,
            });
          },
    }

    if (market) {
      updateMarket.mutate({ ...values, id: market.id }, commonCallbacks);
    } else {
      createMarket.mutate(values, commonCallbacks);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[425px] bg-gray-900 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white">{market ? 'Edit Market' : 'Create Market'}</DialogTitle>
          <DialogDescription className="text-gray-400">
            {market ? 'Update the details of your market.' : 'Add a new market to your list.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 pt-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-300">Market Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Etsy" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-[1fr,auto] gap-4 items-end">
              <FormField
                control={form.control}
                name="transaction_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-300">Transaction Fee</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="transaction_fee_type"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-20"><SelectValue /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">%</SelectItem>
                        <SelectItem value="flat">$</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="grid grid-cols-[1fr,auto] gap-4 items-end">
              <FormField
                control={form.control}
                name="processing_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-300">Processing Fee</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="processing_fee_type"
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                       <FormControl>
                        <SelectTrigger className="w-20"><SelectValue /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">%</SelectItem>
                        <SelectItem value="flat">$</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-[1fr,auto] gap-4 items-end">
              <FormField
                control={form.control}
                name="other_fee"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-gray-300">Other Fee</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="other_fee_type"
                render={({ field }) => (
                  <FormItem>
                     <Select onValueChange={field.onChange} defaultValue={field.value}>
                       <FormControl>
                        <SelectTrigger className="w-20"><SelectValue /></SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">%</SelectItem>
                        <SelectItem value="flat">$</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="pt-4">
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>Cancel</Button>
              <Button type="submit" disabled={createMarket.isPending || updateMarket.isPending}>
                {market ? 'Save Changes' : 'Create Market'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
