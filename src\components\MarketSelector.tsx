
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useMarkets } from "@/hooks/useMarkets";
import { formatFeeDisplay } from "@/utils/feeCalculator";
import { Market } from "@/types/database";

interface MarketSelectorProps {
  selectedMarket: string;
  onMarketChange: (market: string) => void;
  marketData?: Market;
}

export const MarketSelector = ({ selectedMarket, onMarketChange, marketData }: MarketSelectorProps) => {
  const { data: markets = [], isLoading } = useMarkets();

  if (isLoading) {
    return <div className="text-muted-foreground">Loading markets...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col lg:flex-row lg:items-center gap-4">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground font-medium">Selected Market:</span>
          <Select value={selectedMarket} onValueChange={onMarketChange}>
            <SelectTrigger className="w-48 bg-background border-input">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-popover border-border">
              {markets.map((market) => (
                <SelectItem key={market.id} value={market.name} className="hover:bg-accent hover:text-accent-foreground">
                  {market.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {marketData && (
          <div className="flex flex-wrap gap-4">
            <div className="bg-muted/50 px-3 py-1.5 rounded-lg">
              <span className="text-xs text-muted-foreground font-medium uppercase tracking-wide">Transaction Fee: </span>
              <span className="text-xs font-semibold text-foreground">
                {formatFeeDisplay(marketData.transaction_fee, marketData.transaction_fee_type)}
              </span>
            </div>
            <div className="bg-muted/50 px-3 py-1.5 rounded-lg">
              <span className="text-xs text-muted-foreground font-medium uppercase tracking-wide">Processing Fee: </span>
              <span className="text-xs font-semibold text-foreground">
                {formatFeeDisplay(marketData.processing_fee, marketData.processing_fee_type)}
              </span>
            </div>
            <div className="bg-muted/50 px-3 py-1.5 rounded-lg">
              <span className="text-xs text-muted-foreground font-medium uppercase tracking-wide">Other Fee: </span>
              <span className="text-xs font-semibold text-foreground">
                {formatFeeDisplay(marketData.other_fee, marketData.other_fee_type)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
