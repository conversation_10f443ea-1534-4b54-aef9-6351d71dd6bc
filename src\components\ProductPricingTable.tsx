import { useState } from "react";
import {
  useDeleteTrackedProduct,
  useDeleteMultipleTrackedProducts,
  useUpdateMultipleTrackedProducts,
} from "@/hooks/useTrackedProducts";
import { calculateProfit } from "@/utils/feeCalculator";
import { Market, TrackedProduct } from "@/types/database";
import { useToast } from "@/hooks/use-toast";
import { ProductRow } from "./ProductRow";
import { BlueprintHeaderRow } from "./BlueprintHeaderRow";
import { ProductTableHeader } from "./ProductTableHeader";
import { VariantGroupRow } from "./VariantGroupRow";
import { ConfirmDialog } from "./ConfirmDialog";

interface ProductPricingTableProps {
  selectedMarket: string;
  market?: Market;
  products: TrackedProduct[];
  isLoading: boolean;
  selectedProducts: string[];
  onSelectionChange: (ids: string[]) => void;
}

export const ProductPricingTable = ({
  selectedMarket,
  market,
  products,
  isLoading,
  selectedProducts,
  onSelectionChange,
}: ProductPricingTableProps) => {
  const deleteProduct = useDeleteTrackedProduct();
  const deleteMultipleProducts = useDeleteMultipleTrackedProducts();
  const updateMultipleProducts = useUpdateMultipleTrackedProducts();
  const { toast } = useToast();

  const [collapsedBlueprints, setCollapsedBlueprints] = useState<string[]>([]);
  const [collapsedVariantGroups, setCollapsedVariantGroups] = useState<
    string[]
  >([]);
  const [showDeleteSelectedConfirm, setShowDeleteSelectedConfirm] =
    useState(false);

  // Helper function to get the active blueprint (the one with selected variants)
  const getActiveBlueprint = () => {
    if (selectedProducts.length === 0) return null;
    const selectedProduct = products.find((p) =>
      selectedProducts.includes(p.id)
    );
    return selectedProduct?.blueprints?.title || null;
  };

  // Helper function to extract variant attributes from a product
  const extractVariantAttributes = (product: TrackedProduct) => {
    const attributes: Record<string, string> = {};

    // Parse variant title to extract attributes
    const variantParts = product.variant_title.split(" / ");
    variantParts.forEach((part) => {
      if (part.includes(":")) {
        const [key, value] = part.split(":").map((s) => s.trim());
        attributes[key.toLowerCase()] = value;
      } else {
        // Handle cases where it's just "Size Value" or "Color Value"
        const trimmed = part.trim();
        if (trimmed.match(/^(XS|S|M|L|XL|2XL|3XL|4XL|5XL)$/)) {
          attributes.size = trimmed;
        } else if (
          trimmed.match(
            /^(Red|Blue|Green|Black|White|Yellow|Purple|Pink|Gray|Grey|Orange|Brown|Navy|Maroon|Teal|Cyan|Magenta|Lime|Olive|Silver|Gold)$/i
          )
        ) {
          attributes.color = trimmed;
        }
      }
    });

    return attributes;
  };

  // Determine the best grouping attribute for a blueprint
  const getBestGroupingAttribute = (blueprintProducts: TrackedProduct[]) => {
    const attributeCounts: Record<string, Set<string>> = {};

    blueprintProducts.forEach((product) => {
      const attributes = extractVariantAttributes(product);
      Object.entries(attributes).forEach(([key, value]) => {
        if (!attributeCounts[key]) {
          attributeCounts[key] = new Set();
        }
        attributeCounts[key].add(value);
      });
    });

    // Prefer 'size' if it exists and has multiple values
    if (attributeCounts.size && attributeCounts.size.size > 1) {
      return "size";
    }

    // Otherwise, pick the attribute with the most variety
    let bestAttribute = "size"; // default
    let maxVariety = 0;

    Object.entries(attributeCounts).forEach(([key, values]) => {
      if (values.size > maxVariety) {
        maxVariety = values.size;
        bestAttribute = key;
      }
    });

    return bestAttribute;
  };

  // Custom sorting function for sizes
  const sortSizes = (a: string, b: string) => {
    const sizeOrder = ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL", "5XL"];
    const aIndex = sizeOrder.indexOf(a);
    const bIndex = sizeOrder.indexOf(b);

    if (aIndex !== -1 && bIndex !== -1) {
      return aIndex - bIndex;
    } else if (aIndex !== -1) {
      return -1;
    } else if (bIndex !== -1) {
      return 1;
    }

    return a.localeCompare(b);
  };

  // Group products by blueprint
  const groupedProducts = products.reduce((acc, product) => {
    const blueprintTitle = product.blueprints?.title || "Unknown Blueprint";
    if (!acc[blueprintTitle]) {
      acc[blueprintTitle] = [];
    }
    acc[blueprintTitle].push(product);
    return acc;
  }, {} as Record<string, TrackedProduct[]>);

  const toggleProductSelection = (productId: string) => {
    const product = products.find((p) => p.id === productId);
    if (!product) return;

    const productBlueprint = product.blueprints?.title || "Unknown Blueprint";
    const activeBlueprint = getActiveBlueprint();

    // If selecting a new product and there's already an active blueprint from a different product
    if (
      !selectedProducts.includes(productId) &&
      activeBlueprint &&
      activeBlueprint !== productBlueprint
    ) {
      toast({
        title: "Selection Constraint",
        description: `You can only edit variants from one product at a time. Currently editing: ${activeBlueprint}`,
        variant: "destructive",
      });
      return;
    }

    // Proceed with normal selection logic
    onSelectionChange(
      selectedProducts.includes(productId)
        ? selectedProducts.filter((id) => id !== productId)
        : [...selectedProducts, productId]
    );
  };

  const toggleBlueprintCollapse = (blueprint: string) => {
    setCollapsedBlueprints((prev) =>
      prev.includes(blueprint)
        ? prev.filter((b) => b !== blueprint)
        : [...prev, blueprint]
    );
  };

  const toggleVariantGroupCollapse = (groupKey: string) => {
    setCollapsedVariantGroups((prev) =>
      prev.includes(groupKey)
        ? prev.filter((g) => g !== groupKey)
        : [...prev, groupKey]
    );
  };

  const areAllBlueprintProductsSelected = (
    blueprintProducts: TrackedProduct[]
  ) => {
    if (blueprintProducts.length === 0) return false;
    const blueprintProductIds = blueprintProducts.map((p) => p.id);
    return blueprintProductIds.every((id) => selectedProducts.includes(id));
  };

  const handleBlueprintSelectionToggle = (
    blueprintProducts: TrackedProduct[]
  ) => {
    const blueprintTitle =
      blueprintProducts[0]?.blueprints?.title || "Unknown Blueprint";
    const activeBlueprint = getActiveBlueprint();

    // Check if trying to select a different blueprint when another is active
    if (
      activeBlueprint &&
      activeBlueprint !== blueprintTitle &&
      selectedProducts.length > 0
    ) {
      toast({
        title: "Selection Constraint",
        description: `You can only edit variants from one product at a time. Currently editing: ${activeBlueprint}`,
        variant: "destructive",
      });
      return;
    }

    const blueprintProductIds = blueprintProducts.map((p) => p.id);
    const allSelected = areAllBlueprintProductsSelected(blueprintProducts);

    let newSelectedProducts: string[];
    if (allSelected) {
      newSelectedProducts = selectedProducts.filter(
        (id) => !blueprintProductIds.includes(id)
      );
    } else {
      newSelectedProducts = [
        ...new Set([...selectedProducts, ...blueprintProductIds]),
      ];
    }
    onSelectionChange(newSelectedProducts);
  };

  const handleDeleteProduct = async (productId: string) => {
    try {
      await deleteProduct.mutateAsync(productId);
      toast({
        title: "Success",
        description: "Product deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete product",
        variant: "destructive",
      });
    }
  };

  const handleDeleteSelectedProductsConfirm = () => {
    setShowDeleteSelectedConfirm(true);
  };

  const handleDeleteSelectedProducts = async () => {
    if (selectedProducts.length === 0) return;

    try {
      await deleteMultipleProducts.mutateAsync(selectedProducts);
      onSelectionChange([]);
      toast({
        title: "Success",
        description: `${selectedProducts.length} products deleted successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete selected products",
        variant: "destructive",
      });
    }
  };

  const handleDeleteBlueprintProducts = async (
    blueprint: string,
    blueprintProducts: TrackedProduct[]
  ) => {
    if (blueprintProducts.length === 0) return;

    const productIdsToDelete = blueprintProducts.map((p) => p.id);

    try {
      await deleteMultipleProducts.mutateAsync(productIdsToDelete);
      toast({
        title: "Success",
        description: `All variants from "${blueprint}" have been deleted.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete products.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteGroupProducts = async (groupProducts: TrackedProduct[]) => {
    if (groupProducts.length === 0) return;

    const productIdsToDelete = groupProducts.map((p) => p.id);
    const groupValue =
      groupProducts[0]?.variant_title.split(" / ")[0] || "variants";

    try {
      await deleteMultipleProducts.mutateAsync(productIdsToDelete);
      toast({
        title: "Success",
        description: `All ${groupValue} variants have been deleted.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete variants.",
        variant: "destructive",
      });
    }
  };

  // Shipping costs are now automatically fetched when products are added

  const handleBlueprintFreeShippingToggle = async (
    blueprintProducts: TrackedProduct[],
    isFreeShipping: boolean
  ) => {
    if (!market) {
      toast({
        title: "Error",
        description: "Please select a market first.",
        variant: "destructive",
      });
      return;
    }

    const updates = blueprintProducts.map((p) => {
      const currentPrice = p.retail_price || 0;

      const priceAdjustment = isFreeShipping
        ? p.shipping_cost
        : -p.shipping_cost;
      const newPrice = currentPrice + priceAdjustment;

      const newProductData = {
        ...p,
        retail_price: newPrice,
        offers_free_shipping: isFreeShipping,
      };

      const newProfit = calculateProfit(newProductData, market);
      const newMargin = newPrice > 0 ? (newProfit / newPrice) * 100 : 0;

      return {
        id: p.id,
        data: {
          offers_free_shipping: isFreeShipping,
          retail_price: parseFloat(newPrice.toFixed(2)),
          profit_margin_percentage: Math.round(newMargin),
        },
      };
    });

    try {
      await updateMultipleProducts.mutateAsync(updates);
      toast({
        title: "Success",
        description: "Free shipping settings updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update free shipping settings.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="text-center py-8 text-muted-foreground text-sm">
        Loading products...
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground text-sm">
        No products tracked yet. Add products from the Printify catalog to get
        started.
      </div>
    );
  }

  const activeBlueprint = getActiveBlueprint();

  return (
    <div className="bg-background rounded-lg overflow-hidden border border-border shadow-sm">
      {activeBlueprint && selectedProducts.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800 px-4 py-2">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            <span className="font-medium">Active Product:</span>{" "}
            {activeBlueprint}
            <span className="ml-2 text-blue-600 dark:text-blue-300">
              ({selectedProducts.length} variant
              {selectedProducts.length === 1 ? "" : "s"} selected)
            </span>
          </p>
        </div>
      )}

      {/* Scrollable Table Container with Fixed Header */}
      <div className="max-h-[600px] overflow-auto">
        <table className="w-full text-xs">
          {/* Sticky Header */}
          <thead className="sticky top-0 z-10">
            <ProductTableHeader
              products={products}
              selectedProducts={selectedProducts}
              deleteMultipleProductsPending={deleteMultipleProducts.isPending}
              onSelectionChange={onSelectionChange}
              onDeleteSelectedProducts={handleDeleteSelectedProductsConfirm}
            />
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {Object.entries(groupedProducts)
              .sort((a, b) => a[0].localeCompare(b[0]))
              .map(([blueprint, blueprintProducts]) => {
                const isCollapsed = collapsedBlueprints.includes(blueprint);
                const allInBlueprintSelected =
                  areAllBlueprintProductsSelected(blueprintProducts);
                const groupingAttribute =
                  getBestGroupingAttribute(blueprintProducts);
                const isActiveBlueprint = activeBlueprint === blueprint;
                const isBlocked =
                  activeBlueprint && activeBlueprint !== blueprint;

                // Group variants within this blueprint
                const variantGroups: Record<string, TrackedProduct[]> = {};
                blueprintProducts.forEach((product) => {
                  const attributes = extractVariantAttributes(product);
                  const groupValue =
                    attributes[groupingAttribute] || `No ${groupingAttribute}`;
                  if (!variantGroups[groupValue]) {
                    variantGroups[groupValue] = [];
                  }
                  variantGroups[groupValue].push(product);
                });

                const blueprintRows = [
                  <BlueprintHeaderRow
                    key={`header-${blueprint}`}
                    blueprint={blueprint}
                    blueprintProducts={blueprintProducts}
                    isCollapsed={isCollapsed}
                    allInBlueprintSelected={allInBlueprintSelected}
                    updateMultipleProductsPending={
                      updateMultipleProducts.isPending
                    }
                    deleteMultipleProductsPending={
                      deleteMultipleProducts.isPending
                    }
                    onToggleCollapse={toggleBlueprintCollapse}
                    onBlueprintSelectionToggle={handleBlueprintSelectionToggle}
                    onDeleteBlueprintProducts={handleDeleteBlueprintProducts}
                    onBlueprintFreeShippingToggle={
                      handleBlueprintFreeShippingToggle
                    }
                    isActiveBlueprint={isActiveBlueprint}
                    isBlocked={isBlocked}
                  />,
                ];

                if (!isCollapsed) {
                  Object.entries(variantGroups)
                    .sort((a, b) => {
                      if (groupingAttribute === "size") {
                        return sortSizes(a[0], b[0]);
                      }
                      return a[0].localeCompare(b[0]);
                    })
                    .forEach(([groupValue, groupProducts]) => {
                      const groupKey = `${blueprint}-${groupValue}`;
                      const isGroupCollapsed =
                        collapsedVariantGroups.includes(groupKey);

                      blueprintRows.push(
                        <VariantGroupRow
                          key={groupKey}
                          blueprint={blueprint}
                          groupValue={groupValue}
                          groupProducts={groupProducts}
                          groupingAttribute={groupingAttribute}
                          isCollapsed={isGroupCollapsed}
                          selectedProducts={selectedProducts}
                          onToggleCollapse={toggleVariantGroupCollapse}
                          onGroupSelectionToggle={(products) => {
                            const productBlueprint =
                              products[0]?.blueprints?.title ||
                              "Unknown Blueprint";

                            // Check constraint before group selection
                            if (
                              activeBlueprint &&
                              activeBlueprint !== productBlueprint &&
                              selectedProducts.length > 0
                            ) {
                              toast({
                                title: "Selection Constraint",
                                description: `You can only edit variants from one product at a time. Currently editing: ${activeBlueprint}`,
                                variant: "destructive",
                              });
                              return;
                            }

                            const groupProductIds = products.map((p) => p.id);
                            const allSelected = groupProductIds.every((id) =>
                              selectedProducts.includes(id)
                            );

                            let newSelection: string[];
                            if (allSelected) {
                              newSelection = selectedProducts.filter(
                                (id) => !groupProductIds.includes(id)
                              );
                            } else {
                              newSelection = [
                                ...new Set([
                                  ...selectedProducts,
                                  ...groupProductIds,
                                ]),
                              ];
                            }
                            onSelectionChange(newSelection);
                          }}
                          onDeleteGroupProducts={handleDeleteGroupProducts}
                          isBlocked={isBlocked}
                        />
                      );

                      if (!isGroupCollapsed) {
                        groupProducts
                          .sort((a, b) =>
                            a.variant_title.localeCompare(b.variant_title)
                          )
                          .forEach((product) => {
                            blueprintRows.push(
                              <ProductRow
                                key={product.id}
                                product={product}
                                market={market}
                                isSelected={selectedProducts.includes(
                                  product.id
                                )}
                                onToggleSelection={toggleProductSelection}
                                onDelete={handleDeleteProduct}
                                isDeleting={deleteProduct.isPending}
                                isBlocked={isBlocked}
                              />
                            );
                          });
                      }
                    });
                }

                return blueprintRows;
              })}
          </tbody>
        </table>
      </div>

      <ConfirmDialog
        open={showDeleteSelectedConfirm}
        onOpenChange={setShowDeleteSelectedConfirm}
        onConfirm={handleDeleteSelectedProducts}
        title="Delete All Products"
        description="Are you sure you want to delete all selected products and their variants? This action cannot be undone."
        confirmText="Delete All"
        isLoading={deleteMultipleProducts.isPending}
      />
    </div>
  );
};
