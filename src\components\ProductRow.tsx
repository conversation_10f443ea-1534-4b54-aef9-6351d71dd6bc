import React, { useState, useEffect } from "react";
import { TrackedProduct, Market } from "@/types/database";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { useUpdateMultipleTrackedProducts } from "@/hooks/useTrackedProducts";
import { useVariantShippingCosts } from "@/hooks/useShipping";
import {
  calculateFees,
  calculateProfit,
  calculatePriceFromProfit,
  calculatePriceFromMargin,
  calculateDiscountedPrice,
} from "@/utils/feeCalculator";
import { useToast } from "@/hooks/use-toast";
import { EditableTableCell } from "./EditableTableCell";
import { ConfirmDialog } from "./ConfirmDialog";

interface ProductRowProps {
  product: TrackedProduct;
  market: Market | undefined;
  isSelected: boolean;
  onToggleSelection: (id: string) => void;
  onDelete: (id: string) => void;
  isDeleting: boolean;
  isBlocked?: boolean;
}

export const ProductRow = ({
  product,
  market,
  isSelected,
  onToggleSelection,
  onDelete,
  isDeleting,
  isBlocked = false,
}: ProductRowProps) => {
  const { toast } = useToast();
  const updateProduct = useUpdateMultipleTrackedProducts();
  const { data: shippingCosts = [] } = useVariantShippingCosts(product.id);

  const [currentShippingCost, setCurrentShippingCost] = useState(
    product.shipping_cost || 0
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Find current shipping cost based on selected country and method
  useEffect(() => {
    if (
      product.default_shipping_country_id &&
      product.default_shipping_method_id &&
      shippingCosts.length > 0
    ) {
      const matchingCost = shippingCosts.find(
        (cost) =>
          cost.country_id === product.default_shipping_country_id &&
          cost.shipping_method_id === product.default_shipping_method_id
      );

      if (matchingCost) {
        setCurrentShippingCost(matchingCost.first_item_cost);
      } else {
        // Fallback to first available shipping cost
        setCurrentShippingCost(shippingCosts[0].first_item_cost);
      }
    } else if (shippingCosts.length > 0) {
      // If no default country/method, use first available
      setCurrentShippingCost(shippingCosts[0].first_item_cost);
    } else {
      // Final fallback to product's base shipping cost
      setCurrentShippingCost(product.shipping_cost || 0);
    }
  }, [
    shippingCosts,
    product.default_shipping_country_id,
    product.default_shipping_method_id,
    product.shipping_cost,
    product.id,
  ]);

  const discountedPrice = product.retail_price
    ? calculateDiscountedPrice(
        product.retail_price,
        product.discount_percentage
      )
    : 0;
  const fees = market ? calculateFees(discountedPrice, market) : { total: 0 };
  const profit = market
    ? calculateProfit(product, market, currentShippingCost)
    : 0;

  const handleUpdate = async (data: Partial<Omit<TrackedProduct, "id">>) => {
    if (updateProduct.isPending) return;
    try {
      await updateProduct.mutateAsync([{ id: product.id, data }]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update product.",
        variant: "destructive",
      });
    }
  };

  const handlePriceChange = (newPrice: number) => {
    if (!market) return;
    const newDiscountedPrice = calculateDiscountedPrice(
      newPrice,
      product.discount_percentage
    );
    const newProfit = calculateProfit(
      { ...product, retail_price: newPrice },
      market,
      currentShippingCost
    );
    const newMargin =
      newDiscountedPrice > 0 ? (newProfit / newDiscountedPrice) * 100 : 0;
    handleUpdate({
      retail_price: parseFloat(newPrice.toFixed(2)),
      profit_margin_percentage: Math.round(newMargin),
    });
  };

  const handleDiscountChange = (newDiscount: number) => {
    if (!market || !product.retail_price) return;
    const newDiscountedPrice = calculateDiscountedPrice(
      product.retail_price,
      newDiscount
    );
    const newProfit = calculateProfit(
      { ...product, discount_percentage: newDiscount },
      market,
      currentShippingCost
    );
    const newMargin =
      newDiscountedPrice > 0 ? (newProfit / newDiscountedPrice) * 100 : 0;
    handleUpdate({
      discount_percentage: Math.round(newDiscount),
      profit_margin_percentage: Math.round(newMargin),
    });
  };

  const handleProfitChange = (newProfit: number) => {
    if (!market) return;
    const newPrice = calculatePriceFromProfit(
      newProfit,
      product,
      market,
      currentShippingCost
    );
    if (newPrice === null || newPrice < 0) {
      toast({
        title: "Calculation Error",
        description: "Could not calculate a valid price for the given profit.",
        variant: "destructive",
      });
      return;
    }
    const newDiscountedPrice = calculateDiscountedPrice(
      newPrice,
      product.discount_percentage
    );
    const newMargin =
      newDiscountedPrice > 0 ? (newProfit / newDiscountedPrice) * 100 : 0;
    handleUpdate({
      retail_price: parseFloat(newPrice.toFixed(2)),
      profit_margin_percentage: Math.round(newMargin),
    });
  };

  const handleMarginChange = (newMargin: number) => {
    if (!market) return;
    const newPrice = calculatePriceFromMargin(
      newMargin,
      product,
      market,
      currentShippingCost
    );
    if (newPrice === null || newPrice < 0) {
      toast({
        title: "Invalid margin",
        description:
          "The specified profit margin is too high with current market fees.",
        variant: "destructive",
      });
      return;
    }
    handleUpdate({
      retail_price: parseFloat(newPrice.toFixed(2)),
      profit_margin_percentage: Math.round(newMargin),
    });
  };

  const isEditingDisabled = !market || updateProduct.isPending || isBlocked;
  const hasDiscount = product.discount_percentage > 0;

  return (
    <tr
      className={`hover:bg-muted/50 transition-colors border-b border-border text-xs ${
        isSelected
          ? "bg-accent/50 hover:bg-accent/70"
          : "bg-card hover:bg-muted/30"
      } ${updateProduct.isPending ? "opacity-50 pointer-events-none" : ""} ${
        isBlocked ? "opacity-60" : ""
      }`}
    >
      <td className="p-3 align-middle pl-12">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggleSelection(product.id)}
          disabled={isBlocked}
        />
      </td>
      <td className="p-3 text-foreground align-middle font-medium">
        {product.variant_title}
      </td>
      <td className="p-3 text-muted-foreground align-middle">
        {product.print_providers?.title || "Unknown"}
      </td>
      <td className="p-3 text-amber-400 font-medium align-middle">
        ${product.production_cost}
      </td>

      <td className="p-3 text-amber-400 font-medium align-middle">
        ${currentShippingCost.toFixed(2)}
      </td>

      <EditableTableCell
        initialValue={product.discount_percentage}
        onSave={handleDiscountChange}
        suffix="%"
        className="text-blue-400 font-medium"
        disabled={isEditingDisabled}
      />

      <EditableTableCell
        initialValue={product.profit_margin_percentage}
        onSave={handleMarginChange}
        suffix="%"
        className="text-blue-400 font-medium"
        disabled={isEditingDisabled}
      />

      <td className="p-3 text-purple-400 font-medium align-middle">
        {hasDiscount ? (
          <div className="flex flex-col">
            <span className="line-through text-muted-foreground text-xs">
              ${(product.retail_price || 0).toFixed(2)}
            </span>
            <span className="text-purple-400 font-medium">
              ${discountedPrice.toFixed(2)}
            </span>
          </div>
        ) : (
          <span>${(product.retail_price || 0).toFixed(2)}</span>
        )}
      </td>

      <td className="p-3 text-red-400 font-medium align-middle">
        ${fees.total.toFixed(2)}
      </td>

      <EditableTableCell
        initialValue={profit}
        onSave={handleProfitChange}
        prefix="$"
        className={`font-semibold ${
          profit > 0 ? "text-green-400" : "text-red-400"
        }`}
        disabled={isEditingDisabled}
      />

      <td className="p-3 align-middle">
        <Button
          size="sm"
          variant="ghost"
          className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-400/10 transition-colors"
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isDeleting || isBlocked}
        >
          <Trash2 className="h-4 w-4" />
        </Button>

        <ConfirmDialog
          open={showDeleteConfirm}
          onOpenChange={setShowDeleteConfirm}
          onConfirm={() => onDelete(product.id)}
          title="Delete Variant"
          description="Are you sure you want to delete this variant? This action cannot be undone."
          confirmText="Delete"
          isLoading={isDeleting}
        />
      </td>
    </tr>
  );
};
