import React from "react";
import { TrackedProduct } from "@/types/database";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";

interface ProductTableHeaderProps {
  products: TrackedProduct[];
  selectedProducts: string[];
  deleteMultipleProductsPending: boolean;
  onSelectionChange: (ids: string[]) => void;
  onDeleteSelectedProducts: () => void;
}

export const ProductTableHeader = ({
  products,
  selectedProducts,
  deleteMultipleProductsPending,
  onSelectionChange,
  onDeleteSelectedProducts,
}: ProductTableHeaderProps) => {
  const allSelected =
    products.length > 0 && selectedProducts.length === products.length;
  const someSelected =
    selectedProducts.length > 0 && selectedProducts.length < products.length;

  const handleSelectAll = () => {
    if (allSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(products.map((p) => p.id));
    }
  };

  return (
    <tr className="bg-card/95 backdrop-blur-sm border-b border-border text-xs font-medium text-muted-foreground shadow-sm">
      <th className="text-left p-3 pl-12">
        <Checkbox
          checked={allSelected ? true : someSelected ? "indeterminate" : false}
          onCheckedChange={handleSelectAll}
        />
      </th>
      <th className="text-left p-3 text-xs font-medium text-muted-foreground uppercase tracking-wider">
        VARIANT
      </th>
      <th className="text-left p-3 text-xs font-medium text-muted-foreground uppercase tracking-wider">
        PROVIDER
      </th>
      <th className="text-left p-3 text-xs font-medium text-amber-400 uppercase tracking-wider">
        PRODUCTION
      </th>
      <th className="text-left p-3 text-xs font-medium text-amber-400 uppercase tracking-wider">
        SHIPPING
      </th>
      <th className="text-left p-3 text-xs font-medium text-blue-400 uppercase tracking-wider">
        DISCOUNT
      </th>
      <th className="text-left p-3 text-xs font-medium text-blue-400 uppercase tracking-wider">
        MARGIN
      </th>
      <th className="text-left p-3 text-xs font-medium text-purple-400 uppercase tracking-wider">
        PRICE
      </th>
      <th className="text-left p-3 text-xs font-medium text-red-400 uppercase tracking-wider">
        FEES
      </th>
      <th className="text-left p-3 text-xs font-medium text-green-400 uppercase tracking-wider">
        PROFIT
      </th>
      <th className="text-left p-3">
        {selectedProducts.length > 0 && (
          <Button
            size="sm"
            variant="ghost"
            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-400/10 transition-colors"
            onClick={onDeleteSelectedProducts}
            disabled={deleteMultipleProductsPending}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </th>
    </tr>
  );
};
