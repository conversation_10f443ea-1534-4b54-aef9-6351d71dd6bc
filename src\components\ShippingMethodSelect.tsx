
import React, { useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useShippingMethods } from '@/hooks/useShipping';
import { ShippingMethod } from '@/types/database';

interface ShippingMethodSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  defaultMethod?: string;
}

export const ShippingMethodSelect = ({ value, onValueChange, disabled, defaultMethod = "Standard" }: ShippingMethodSelectProps) => {
  const { data: shippingMethods = [], isLoading } = useShippingMethods();

  const selectedMethod = shippingMethods.find(sm => sm.id === value);

  // Set default value if none is selected and we have methods loaded
  useEffect(() => {
    if (!value && shippingMethods.length > 0 && !isLoading) {
      const defaultMethodData = shippingMethods.find(sm => sm.name === defaultMethod);
      if (defaultMethodData) {
        onValueChange(defaultMethodData.id);
      }
    }
  }, [value, shippingMethods, isLoading, defaultMethod, onValueChange]);

  return (
    <Select value={value} onValueChange={onValueChange} disabled={disabled || isLoading}>
      <SelectTrigger className="w-20 h-8 text-xs bg-background border border-border">
        <SelectValue placeholder={isLoading ? "Loading..." : selectedMethod?.name || defaultMethod} />
      </SelectTrigger>
      <SelectContent className="bg-background border border-border shadow-lg z-50">
        {shippingMethods.map((method) => (
          <SelectItem key={method.id} value={method.id} className="text-xs">
            {method.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
