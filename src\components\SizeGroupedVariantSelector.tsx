
import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface Variant {
  id: number;
  title: string;
  options: {
    color?: string;
    size?: string;
    [key: string]: any;
  };
}

interface VariantGroup {
  groupValue: string;
  variants: Variant[];
}

interface SizeGroupedVariantSelectorProps {
  variants: Variant[];
  selectedVariantIds: number[];
  onSelectionChange: (variantIds: number[]) => void;
  groupingAttribute?: string;
}

export const SizeGroupedVariantSelector = ({
  variants,
  selectedVariantIds,
  onSelectionChange,
  groupingAttribute = "size"
}: SizeGroupedVariantSelectorProps) => {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  // Group variants by the specified attribute
  const variantGroups: VariantGroup[] = React.useMemo(() => {
    const groups: Record<string, Variant[]> = {};
    
    variants.forEach(variant => {
      const groupValue = variant.options?.[groupingAttribute] || `No ${groupingAttribute.charAt(0).toUpperCase() + groupingAttribute.slice(1)}`;
      if (!groups[groupValue]) {
        groups[groupValue] = [];
      }
      groups[groupValue].push(variant);
    });

    return Object.entries(groups)
      .map(([groupValue, variants]) => ({ groupValue, variants }))
      .sort((a, b) => {
        // Custom sort order for common sizes
        if (groupingAttribute === 'size') {
          const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL'];
          const aIndex = sizeOrder.indexOf(a.groupValue);
          const bIndex = sizeOrder.indexOf(b.groupValue);
          
          if (aIndex !== -1 && bIndex !== -1) {
            return aIndex - bIndex;
          } else if (aIndex !== -1) {
            return -1;
          } else if (bIndex !== -1) {
            return 1;
          }
        }
        return a.groupValue.localeCompare(b.groupValue);
      });
  }, [variants, groupingAttribute]);

  const toggleGroupExpansion = (groupValue: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupValue)) {
      newExpanded.delete(groupValue);
    } else {
      newExpanded.add(groupValue);
    }
    setExpandedGroups(newExpanded);
  };

  const toggleGroupSelection = (variantGroup: VariantGroup) => {
    const groupVariantIds = variantGroup.variants.map(v => v.id);
    const allGroupSelected = groupVariantIds.every(id => selectedVariantIds.includes(id));
    
    let newSelection: number[];
    if (allGroupSelected) {
      // Deselect all variants in this group
      newSelection = selectedVariantIds.filter(id => !groupVariantIds.includes(id));
    } else {
      // Select all variants in this group
      const uniqueIds = new Set([...selectedVariantIds, ...groupVariantIds]);
      newSelection = Array.from(uniqueIds);
    }
    
    onSelectionChange(newSelection);
  };

  const toggleVariantSelection = (variantId: number) => {
    let newSelection: number[];
    if (selectedVariantIds.includes(variantId)) {
      newSelection = selectedVariantIds.filter(id => id !== variantId);
    } else {
      newSelection = [...selectedVariantIds, variantId];
    }
    onSelectionChange(newSelection);
  };

  const getGroupSelectionState = (variantGroup: VariantGroup) => {
    const groupVariantIds = variantGroup.variants.map(v => v.id);
    const selectedCount = groupVariantIds.filter(id => selectedVariantIds.includes(id)).length;
    
    if (selectedCount === 0) return 'none';
    if (selectedCount === groupVariantIds.length) return 'all';
    return 'partial';
  };

  const selectAllVariants = () => {
    const allVariantIds = variants.map(v => v.id);
    onSelectionChange(allVariantIds);
  };

  const deselectAllVariants = () => {
    onSelectionChange([]);
  };

  const getOtherAttributeDisplay = (variant: Variant, primaryAttribute: string) => {
    const otherAttributes = Object.entries(variant.options || {})
      .filter(([key, value]) => key !== primaryAttribute && value)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
    
    return otherAttributes || 'No additional attributes';
  };

  const capitalizedAttribute = groupingAttribute.charAt(0).toUpperCase() + groupingAttribute.slice(1);

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium text-gray-300">
          Select Variants ({selectedVariantIds.length} of {variants.length} selected)
        </span>
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={selectAllVariants}
            className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Select All
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={deselectAllVariants}
            className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Clear All
          </Button>
        </div>
      </div>

      <div className="space-y-1">
        {variantGroups.map((variantGroup) => {
          const selectionState = getGroupSelectionState(variantGroup);
          const isExpanded = expandedGroups.has(variantGroup.groupValue);
          
          return (
            <Collapsible key={variantGroup.groupValue} open={isExpanded} onOpenChange={() => toggleGroupExpansion(variantGroup.groupValue)}>
              <div className="flex items-center gap-2 p-3 bg-gray-700/50 rounded border border-gray-600">
                <Checkbox
                  checked={selectionState === 'all'}
                  onCheckedChange={() => toggleGroupSelection(variantGroup)}
                  className={selectionState === 'partial' ? 'data-[state=checked]:bg-orange-500' : ''}
                />
                
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-0 h-auto text-gray-300 hover:text-white"
                  >
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                
                <div className="flex-1 flex items-center justify-between">
                  <span className="font-medium text-white">
                    {capitalizedAttribute}: {variantGroup.groupValue}
                  </span>
                  <span className="text-sm text-gray-400">
                    {variantGroup.variants.filter(v => selectedVariantIds.includes(v.id)).length} of {variantGroup.variants.length} selected
                  </span>
                </div>
              </div>

              <CollapsibleContent className="ml-8 mt-2 space-y-1">
                {variantGroup.variants.map((variant) => (
                  <div 
                    key={variant.id} 
                    className="flex items-center gap-2 p-2 bg-gray-800/30 rounded border border-gray-700"
                  >
                    <Checkbox
                      checked={selectedVariantIds.includes(variant.id)}
                      onCheckedChange={() => toggleVariantSelection(variant.id)}
                    />
                    <span className="text-sm text-gray-300">
                      {getOtherAttributeDisplay(variant, groupingAttribute)} - {variant.title}
                    </span>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          );
        })}
      </div>
    </div>
  );
};
