import React, { useState } from "react";
import { ChevronDown, ChevronRight, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TrackedProduct } from "@/types/database";
import { ConfirmDialog } from "./ConfirmDialog";

interface VariantGroupRowProps {
  blueprint: string;
  groupValue: string;
  groupProducts: TrackedProduct[];
  groupingAttribute: string;
  isCollapsed: boolean;
  selectedProducts: string[];
  onToggleCollapse: (groupKey: string) => void;
  onGroupSelectionToggle: (products: TrackedProduct[]) => void;
  onDeleteGroupProducts: (groupProducts: TrackedProduct[]) => void;
  isBlocked?: boolean;
}

export const VariantGroupRow = ({
  blueprint,
  groupValue,
  groupProducts,
  groupingAttribute,
  isCollapsed,
  selectedProducts,
  onToggleCollapse,
  onGroupSelectionToggle,
  onDeleteGroupProducts,
  isBlocked = false,
}: VariantGroupRowProps) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const groupKey = `${blueprint}-${groupValue}`;
  const groupProductIds = groupProducts.map((p) => p.id);
  const allGroupSelected = groupProductIds.every((id) =>
    selectedProducts.includes(id)
  );
  const someGroupSelected = groupProductIds.some((id) =>
    selectedProducts.includes(id)
  );

  const capitalizedAttribute =
    groupingAttribute.charAt(0).toUpperCase() + groupingAttribute.slice(1);

  // Handle professional display for variant grouping
  const getGroupDisplayText = () => {
    // If groupValue is "No size", "No color", etc., show a more professional text
    if (groupValue.toLowerCase().startsWith("no ")) {
      return `All ${groupProducts.length} variant${
        groupProducts.length === 1 ? "" : "s"
      }`;
    }

    // For meaningful values, show the attribute and value
    return (
      <>
        {capitalizedAttribute}:{" "}
        <span className="text-primary font-semibold">{groupValue}</span>
      </>
    );
  };

  return (
    <tr
      className={`transition-colors border-b border-border ${
        isBlocked
          ? "bg-muted/20 hover:bg-muted/30 opacity-60"
          : "bg-muted/30 hover:bg-muted/50"
      }`}
    >
      <td className="p-3 pl-8">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={allGroupSelected}
            ref={(el) => {
              if (el && "indeterminate" in el) {
                (el as any).indeterminate =
                  someGroupSelected && !allGroupSelected;
              }
            }}
            onCheckedChange={() => onGroupSelectionToggle(groupProducts)}
            disabled={isBlocked}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleCollapse(groupKey)}
            className="text-muted-foreground hover:text-foreground p-0 h-auto hover:bg-transparent"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </td>
      <td className="p-3 text-foreground font-medium" colSpan={9}>
        <div className="flex items-center justify-between">
          <span>{getGroupDisplayText()}</span>
          {someGroupSelected && (
            <span className="text-xs text-muted-foreground/70">
              {
                groupProducts.filter((p) => selectedProducts.includes(p.id))
                  .length
              }{" "}
              of {groupProducts.length} selected
            </span>
          )}
        </div>
      </td>
      <td className="p-3 align-middle">
        <Button
          size="sm"
          variant="ghost"
          className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-400/10 transition-colors"
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isBlocked}
        >
          <Trash2 className="h-3 w-3" />
        </Button>

        <ConfirmDialog
          open={showDeleteConfirm}
          onOpenChange={setShowDeleteConfirm}
          onConfirm={() => onDeleteGroupProducts(groupProducts)}
          title="Delete Variant Group"
          description="Are you sure you want to delete all variants in this group? This action cannot be undone."
          confirmText="Delete All"
          isLoading={false}
        />
      </td>
    </tr>
  );
};
