
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Market } from '@/types/database';

export const useMarkets = () => {
  return useQuery({
    queryKey: ['markets'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('markets')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data as Market[];
    },
  });
};

export const useMarket = (marketName: string) => {
  return useQuery({
    queryKey: ['market', marketName],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('markets')
        .select('*')
        .eq('name', marketName)
        .single();
      
      if (error) throw error;
      return data as Market;
    },
    enabled: !!marketName,
  });
};

export const useCreateMarket = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (market: Partial<Omit<Market, 'id' | 'created_at' | 'updated_at'>>) => {
      const { data, error } = await supabase
        .from('markets')
        .insert([market as Omit<Market, 'id' | 'created_at' | 'updated_at'>])
        .select()
        .single();
      
      if (error) throw error;
      // Supabase returns the created record in an array, so we return the first element.
      // The return type of select().single() is the single object, so this is correct.
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['markets'] });
    },
  });
};

export const useUpdateMarket = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (market: Pick<Market, 'id'> & Partial<Omit<Market, 'id' | 'created_at' | 'updated_at'>>) => {
      const { id, ...updateData } = market;
      const { data, error } = await supabase
        .from('markets')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['markets'] });
    },
  });
};

export const useDeleteMarket = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (marketId: string) => {
      const { error } = await supabase
        .from('markets')
        .delete()
        .eq('id', marketId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['markets'] });
    },
  });
};
