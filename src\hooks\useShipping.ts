import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Country, ShippingMethod, VariantShippingCost } from "@/types/database";

export const useCountries = () => {
  return useQuery({
    queryKey: ["countries"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("countries")
        .select("*")
        .order("name");

      if (error) throw error;
      return data as Country[];
    },
  });
};

export const useShippingMethods = () => {
  return useQuery({
    queryKey: ["shipping-methods"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("shipping_methods")
        .select("*")
        .order("name");

      if (error) throw error;
      return data as ShippingMethod[];
    },
  });
};

export const useVariantShippingCosts = (productId?: string) => {
  return useQuery({
    queryKey: ["variant-shipping-costs", productId],
    queryFn: async () => {
      if (!productId) return [];

      const { data, error } = await supabase
        .from("variant_shipping_costs")
        .select(
          `
          *,
          countries (
            id,
            name,
            code
          ),
          shipping_methods (
            id,
            name,
            code
          )
        `
        )
        .eq("tracked_product_id", productId);

      if (error) throw error;
      return data as (VariantShippingCost & {
        countries: Country;
        shipping_methods: ShippingMethod;
      })[];
    },
    enabled: !!productId,
  });
};

export const useUpdateProductShipping = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      productId,
      countryId,
      shippingMethodId,
    }: {
      productId: string;
      countryId: string;
      shippingMethodId: string;
    }) => {
      const { error } = await supabase
        .from("tracked_products")
        .update({
          default_shipping_country_id: countryId,
          default_shipping_method_id: shippingMethodId,
        })
        .eq("id", productId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["trackedProducts"] });
      queryClient.invalidateQueries({ queryKey: ["variant-shipping-costs"] });
    },
  });
};
