
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { TrackedProduct } from '@/types/database';

export const useTrackedProducts = () => {
  return useQuery({
    queryKey: ['trackedProducts'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tracked_products')
        .select(`
          *,
          blueprints:blueprint_id (
            id,
            title,
            brand,
            model
          ),
          print_providers:print_provider_id (
            id,
            title,
            location
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as TrackedProduct[];
    },
  });
};

export const useInsertTrackedProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (products: Omit<TrackedProduct, 'id' | 'created_at' | 'updated_at'>[]) => {
      const { data, error } = await supabase
        .from('tracked_products')
        .insert(products)
        .select('*');

      if (error) throw error;

      // After inserting products, fetch and save their shipping costs
      if (data && data.length > 0) {
        console.log(`Inserted ${data.length} products, now fetching shipping costs...`);
        
        // Get the IDs of newly inserted products
        const trackedProductIds = data.map(product => product.id);
        
        // Call the edge function to save all shipping costs
        try {
          const { error: shippingError } = await supabase.functions.invoke('printify-catalog', {
            body: {
              action: 'save-all-shipping-costs',
              trackedProductIds
            }
          });

          if (shippingError) {
            console.error('Error saving shipping costs:', shippingError);
          } else {
            console.log('Successfully saved shipping costs for all products');
          }
        } catch (error) {
          console.error('Error calling shipping costs function:', error);
        }
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};

export const useUpdateTrackedProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Omit<TrackedProduct, 'id'>> }) => {
      const { data: result, error } = await supabase
        .from('tracked_products')
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};

export const useUpdateMultipleTrackedProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: Partial<Omit<TrackedProduct, 'id'>> }>) => {
      const updatePromises = updates.map(({ id, data }) =>
        supabase
          .from('tracked_products')
          .update(data)
          .eq('id', id)
          .select()
          .single()
      );

      const results = await Promise.all(updatePromises);
      
      // Check for errors
      results.forEach((result) => {
        if (result.error) throw result.error;
      });

      return results.map(result => result.data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};

export const useDeleteTrackedProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('tracked_products')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};

export const useDeleteMultipleTrackedProducts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (ids: string[]) => {
      const { error } = await supabase
        .from('tracked_products')
        .delete()
        .in('id', ids);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};

export const useRefreshShippingCosts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productIds: string[]) => {
      const { error } = await supabase.functions.invoke('printify-catalog', {
        body: {
          action: 'save-all-shipping-costs',
          trackedProductIds: productIds
        }
      });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['variant-shipping-costs'] });
      queryClient.invalidateQueries({ queryKey: ['trackedProducts'] });
    },
  });
};
