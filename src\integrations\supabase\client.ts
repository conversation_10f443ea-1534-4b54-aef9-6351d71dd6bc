// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://bxqaamhdveonqbriozsd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ4cWFhbWhkdmVvbnFicmlvenNkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTgwOTEsImV4cCI6MjA2NTQ5NDA5MX0.dHoldQmRrW_7TuOkbMFO2c69xwpwXFa95T-g5Sb_3Gc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);