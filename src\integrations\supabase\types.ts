export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      blueprints: {
        Row: {
          brand: string | null
          created_at: string
          description: string | null
          id: string
          model: string | null
          printify_id: number
          title: string
        }
        Insert: {
          brand?: string | null
          created_at?: string
          description?: string | null
          id?: string
          model?: string | null
          printify_id: number
          title: string
        }
        Update: {
          brand?: string | null
          created_at?: string
          description?: string | null
          id?: string
          model?: string | null
          printify_id?: number
          title?: string
        }
        Relationships: []
      }
      countries: {
        Row: {
          code: string
          created_at: string
          id: string
          name: string
        }
        Insert: {
          code: string
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          code?: string
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      markets: {
        Row: {
          created_at: string
          id: string
          name: string
          other_fee: number
          other_fee_type: string
          processing_fee: number
          processing_fee_type: string
          transaction_fee: number
          transaction_fee_type: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          other_fee?: number
          other_fee_type?: string
          processing_fee?: number
          processing_fee_type?: string
          transaction_fee?: number
          transaction_fee_type?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          other_fee?: number
          other_fee_type?: string
          processing_fee?: number
          processing_fee_type?: string
          transaction_fee?: number
          transaction_fee_type?: string
          updated_at?: string
        }
        Relationships: []
      }
      print_providers: {
        Row: {
          created_at: string
          id: string
          location: string | null
          printify_id: number
          title: string
        }
        Insert: {
          created_at?: string
          id?: string
          location?: string | null
          printify_id: number
          title: string
        }
        Update: {
          created_at?: string
          id?: string
          location?: string | null
          printify_id?: number
          title?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string
          first_name: string | null
          id: string
          last_name: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          first_name?: string | null
          id: string
          last_name?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          first_name?: string | null
          id?: string
          last_name?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      shipping_methods: {
        Row: {
          code: string
          created_at: string
          id: string
          name: string
        }
        Insert: {
          code: string
          created_at?: string
          id?: string
          name: string
        }
        Update: {
          code?: string
          created_at?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      tracked_products: {
        Row: {
          blueprint_id: string | null
          color: string | null
          created_at: string
          default_shipping_country_id: string | null
          default_shipping_method_id: string | null
          discount_percentage: number | null
          id: string
          is_active: boolean | null
          notes: string | null
          offers_free_shipping: boolean
          print_provider_id: string | null
          printify_variant_id: number
          production_cost: number
          profit_margin_percentage: number | null
          retail_price: number | null
          shipping_cost: number | null
          size: string | null
          updated_at: string
          variant_title: string
        }
        Insert: {
          blueprint_id?: string | null
          color?: string | null
          created_at?: string
          default_shipping_country_id?: string | null
          default_shipping_method_id?: string | null
          discount_percentage?: number | null
          id?: string
          is_active?: boolean | null
          notes?: string | null
          offers_free_shipping?: boolean
          print_provider_id?: string | null
          printify_variant_id: number
          production_cost: number
          profit_margin_percentage?: number | null
          retail_price?: number | null
          shipping_cost?: number | null
          size?: string | null
          updated_at?: string
          variant_title: string
        }
        Update: {
          blueprint_id?: string | null
          color?: string | null
          created_at?: string
          default_shipping_country_id?: string | null
          default_shipping_method_id?: string | null
          discount_percentage?: number | null
          id?: string
          is_active?: boolean | null
          notes?: string | null
          offers_free_shipping?: boolean
          print_provider_id?: string | null
          printify_variant_id?: number
          production_cost?: number
          profit_margin_percentage?: number | null
          retail_price?: number | null
          shipping_cost?: number | null
          size?: string | null
          updated_at?: string
          variant_title?: string
        }
        Relationships: [
          {
            foreignKeyName: "tracked_products_blueprint_id_fkey"
            columns: ["blueprint_id"]
            isOneToOne: false
            referencedRelation: "blueprints"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tracked_products_default_shipping_country_id_fkey"
            columns: ["default_shipping_country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tracked_products_default_shipping_method_id_fkey"
            columns: ["default_shipping_method_id"]
            isOneToOne: false
            referencedRelation: "shipping_methods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tracked_products_print_provider_id_fkey"
            columns: ["print_provider_id"]
            isOneToOne: false
            referencedRelation: "print_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      user_api_keys: {
        Row: {
          api_key_encrypted: string
          created_at: string
          id: string
          service_name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          api_key_encrypted: string
          created_at?: string
          id?: string
          service_name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          api_key_encrypted?: string
          created_at?: string
          id?: string
          service_name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      variant_shipping_costs: {
        Row: {
          additional_item_cost: number
          country_id: string
          created_at: string
          delivery_time_max: number | null
          delivery_time_min: number | null
          first_item_cost: number
          id: string
          shipping_method_id: string
          tracked_product_id: string
          updated_at: string
        }
        Insert: {
          additional_item_cost?: number
          country_id: string
          created_at?: string
          delivery_time_max?: number | null
          delivery_time_min?: number | null
          first_item_cost?: number
          id?: string
          shipping_method_id: string
          tracked_product_id: string
          updated_at?: string
        }
        Update: {
          additional_item_cost?: number
          country_id?: string
          created_at?: string
          delivery_time_max?: number | null
          delivery_time_min?: number | null
          first_item_cost?: number
          id?: string
          shipping_method_id?: string
          tracked_product_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "variant_shipping_costs_country_id_fkey"
            columns: ["country_id"]
            isOneToOne: false
            referencedRelation: "countries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "variant_shipping_costs_shipping_method_id_fkey"
            columns: ["shipping_method_id"]
            isOneToOne: false
            referencedRelation: "shipping_methods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "variant_shipping_costs_tracked_product_id_fkey"
            columns: ["tracked_product_id"]
            isOneToOne: false
            referencedRelation: "tracked_products"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
