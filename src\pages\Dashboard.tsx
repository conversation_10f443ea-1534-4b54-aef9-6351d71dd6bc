
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  created_at: string;
  updated_at: string;
}

const Dashboard = () => {
    const { user } = useAuth();
    const [profile, setProfile] = useState<Profile | null>(null);

    const getDisplayName = () => {
        if (profile?.first_name && profile?.last_name) {
            return `${profile.first_name} ${profile.last_name}`;
        } else if (profile?.first_name) {
            return profile.first_name;
        }
        return user?.email?.split('@')[0] || 'User';
    };

    const getGreeting = () => {
        const hour = new Date().getHours();
        if (hour < 12) return 'Good morning';
        if (hour < 17) return 'Good afternoon';
        return 'Good evening';
    };

    useEffect(() => {
        if (user) {
            fetchProfile();
        }
    }, [user]);

    const fetchProfile = async () => {
        try {
            const { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('id', user?.id)
                .maybeSingle();

            if (error && error.code !== 'PGRST116') {
                throw error;
            }

            setProfile(data);
        } catch (error) {
            console.error('Error fetching profile:', error);
        }
    };

    return (
        <div className="p-6 sm:p-10 bg-gradient-to-br from-background to-muted/20 min-h-screen">
            {/* Main Header */}
            <header className="mb-12">
                <div className="mb-4">
                    <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground mb-4">
                        {getGreeting()}, {getDisplayName()}!
                    </h1>
                    <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl leading-relaxed">
                        Welcome to your workspace. Access powerful POD tools to grow and optimize your print-on-demand business.
                    </p>
                </div>
            </header>

            {/* Tools Section */}
            <section className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-8 shadow-lg shadow-black/5">
                <div className="mb-8">
                    <h2 className="text-2xl sm:text-3xl font-semibold text-foreground mb-3">Your Tools</h2>
                    <p className="text-muted-foreground">Powerful tools to help you succeed with your print-on-demand business</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <Card className="bg-background/80 backdrop-blur-sm border-border/60 hover:border-primary/50 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 hover:-translate-y-1 group">
                        <CardHeader className="pb-4">
                            <CardTitle className="text-foreground group-hover:text-primary transition-colors text-lg">
                                Printify Pricing Tool
                            </CardTitle>
                            <CardDescription className="text-muted-foreground leading-relaxed text-sm">
                                Calculate your product prices, profit margins, and fees for different marketplaces with precision.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Button asChild className="w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-md">
                                <Link to="/app/pricing-tool">
                                    Open Tool <ArrowRight className="ml-2 h-4 w-4" />
                                </Link>
                            </Button>
                        </CardContent>
                    </Card>
                    
                    <Card className="bg-background/50 border-dashed border-border/60 hover:border-border transition-colors">
                        <CardHeader className="pb-4">
                            <CardTitle className="text-muted-foreground flex items-center gap-2 text-lg">
                                Coming Soon
                            </CardTitle>
                            <CardDescription className="text-muted-foreground/80 text-sm">
                                More powerful tools are on the way to enhance your business workflow.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-xs text-muted-foreground/60 italic">
                                Stay tuned for updates
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </section>
        </div>
    );
};

export default Dashboard;
