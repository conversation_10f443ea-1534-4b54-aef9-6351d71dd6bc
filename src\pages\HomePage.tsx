import { Link, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ArrowR<PERSON>,
  DollarSign,
  <PERSON><PERSON><PERSON>,
  Zap,
  TrendingUp,
  Shield,
  Clock,
  Star,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useEffect } from "react";
import { ThemeToggle } from "@/components/ThemeToggle";

const HomePage = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && user) {
      navigate("/app", { replace: true });
    }
  }, [user, loading, navigate]);

  if (loading || (!loading && user)) {
    return (
      <div className="bg-background text-foreground min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-background text-foreground min-h-screen flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <img src="/logo.svg" alt="PODToolsy" className="h-8 w-8" />
            <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
              PODToolsy
            </h1>
          </div>
          <nav className="hidden md:flex items-center gap-6">
            <a
              href="#features"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Features
            </a>
            <a
              href="#pricing"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              Pricing
            </a>
            <ThemeToggle />
            <Button asChild variant="outline" size="sm">
              <Link to="/auth">Sign In</Link>
            </Button>
            <Button
              asChild
              size="sm"
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700"
            >
              <Link to="/auth">
                Get Started <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </nav>
          <div className="md:hidden flex items-center gap-2">
            <ThemeToggle />
            <Button asChild size="sm">
              <Link to="/auth">Sign In</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-6 py-24 sm:py-32 text-center">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <span className="inline-flex items-center rounded-full bg-orange-100 dark:bg-orange-900/20 px-3 py-1 text-sm font-medium text-orange-800 dark:text-orange-200 ring-1 ring-orange-600/20">
                🚀 New: Advanced Profit Analytics Coming Soon
              </span>
            </div>
            <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-orange-600 to-orange-700">
              The Ultimate Print-on-Demand Toolkit
            </h2>
            <p className="mt-6 text-xl text-muted-foreground max-w-2xl mx-auto">
              Maximize your profits with powerful pricing tools, automated
              calculations, and data-driven insights. Built specifically for
              Printify sellers who want to scale smart.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Link to="/auth" className="flex items-center">
                  Start Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <a href="#features" className="flex items-center">
                  See How It Works
                  <TrendingUp className="ml-2 h-5 w-5" />
                </a>
              </Button>
            </div>
            <div className="mt-12 flex items-center justify-center gap-8 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-500" />
                Free 14-day trial
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-500" />
                Setup in 2 minutes
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                No credit card required
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section
          id="features"
          className="container mx-auto px-6 py-24 sm:py-32 bg-muted/30"
        >
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-3xl font-bold tracking-tight">
              Everything you need to scale your POD business
            </h3>
            <p className="mt-4 text-muted-foreground">
              From advanced pricing calculations to profit optimization,
              PODToolsy provides the complete toolkit for serious
              print-on-demand entrepreneurs.
            </p>
          </div>
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-card p-8 rounded-xl border border-border hover:border-orange-500/50 transition-all duration-200 hover:shadow-lg">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-gradient-to-r from-orange-500 to-orange-600 text-white mb-6">
                <DollarSign className="h-6 w-6" />
              </div>
              <h4 className="text-xl font-bold mb-3">
                Smart Pricing Calculator
              </h4>
              <p className="text-muted-foreground">
                Instantly calculate optimal prices with automatic fee
                calculations for Etsy, Shopify, Amazon, and more. Factor in
                production costs, shipping, and your target margins.
              </p>
            </div>
            <div className="bg-card p-8 rounded-xl border border-border hover:border-orange-500/50 transition-all duration-200 hover:shadow-lg">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white mb-6">
                <BarChart className="h-6 w-6" />
              </div>
              <h4 className="text-xl font-bold mb-3">Market Intelligence</h4>
              <p className="text-muted-foreground">
                Track competitor pricing, analyze market trends, and get
                insights on the most profitable products in your niche. Make
                data-driven decisions.
              </p>
            </div>
            <div className="bg-card p-8 rounded-xl border border-border hover:border-orange-500/50 transition-all duration-200 hover:shadow-lg">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-gradient-to-r from-green-500 to-green-600 text-white mb-6">
                <TrendingUp className="h-6 w-6" />
              </div>
              <h4 className="text-xl font-bold mb-3">Profit Optimization</h4>
              <p className="text-muted-foreground">
                Automatically optimize your pricing strategy based on market
                conditions, seasonal trends, and profit goals. Maximize revenue
                while staying competitive.
              </p>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="container mx-auto px-6 py-24 sm:py-32">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-3xl font-bold tracking-tight">
              Simple, transparent pricing
            </h3>
            <p className="mt-4 text-muted-foreground">
              Start free, upgrade when you're ready to scale. No hidden fees,
              cancel anytime.
            </p>
          </div>
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Plan */}
            <div className="bg-card p-8 rounded-xl border border-border">
              <h4 className="text-2xl font-bold">Starter</h4>
              <p className="text-muted-foreground mt-2">
                Perfect for getting started
              </p>
              <div className="mt-6">
                <span className="text-4xl font-bold">$0</span>
                <span className="text-muted-foreground">/month</span>
              </div>
              <ul className="mt-6 space-y-3">
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Basic pricing calculator</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Up to 50 products</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Email support</span>
                </li>
              </ul>
              <Button asChild className="w-full mt-8" variant="outline">
                <Link to="/auth">Get Started Free</Link>
              </Button>
            </div>

            {/* Pro Plan */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 p-8 rounded-xl border-2 border-orange-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
              <h4 className="text-2xl font-bold">Professional</h4>
              <p className="text-muted-foreground mt-2">
                For serious POD entrepreneurs
              </p>
              <div className="mt-6">
                <span className="text-4xl font-bold">$19</span>
                <span className="text-muted-foreground">/month</span>
              </div>
              <ul className="mt-6 space-y-3">
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Advanced pricing tools</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Unlimited products</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Market intelligence</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Profit optimization</span>
                </li>
                <li className="flex items-center gap-3">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span>Priority support</span>
                </li>
              </ul>
              <Button
                asChild
                className="w-full mt-8 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700"
              >
                <Link to="/auth">Start Free Trial</Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="container mx-auto px-6 py-24 sm:py-32">
          <div className="relative isolate overflow-hidden bg-gradient-to-br from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-2xl px-6 py-24 text-center shadow-2xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to maximize your POD profits?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
              Join thousands of successful print-on-demand sellers who trust
              PODToolsy to optimize their pricing and boost their profits.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <Link to="/auth">
                  Start Your Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-muted/30 border-t border-border">
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-3 mb-4 md:mb-0">
              <img src="/logo.svg" alt="PODToolsy" className="h-6 w-6" />
              <span className="font-bold text-lg bg-gradient-to-r from-orange-500 to-orange-600 bg-clip-text text-transparent">
                PODToolsy
              </span>
            </div>
            <div className="text-center md:text-right">
              <p className="text-muted-foreground">
                &copy; {new Date().getFullYear()} PODToolsy. All rights
                reserved.
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Built for print-on-demand entrepreneurs
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
