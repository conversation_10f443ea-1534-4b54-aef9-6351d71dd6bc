import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useMarkets, useDeleteMarket } from "@/hooks/useMarkets";
import { Market } from "@/types/database";
import { formatFeeDisplay } from "@/utils/feeCalculator";
import { Pencil, Trash2, PlusCircle } from "lucide-react";
import { MarketFormDialog } from "@/components/MarketFormDialog";
import { ConfirmDialog } from "@/components/ConfirmDialog";
import { useToast } from "@/hooks/use-toast";

const MarketsPage = () => {
  const { data: markets = [], isLoading } = useMarkets();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedMarket, setSelectedMarket] = useState<Market | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [marketToDelete, setMarketToDelete] = useState<Market | null>(null);
  const deleteMarketMutation = useDeleteMarket();
  const { toast } = useToast();

  const handleEdit = (market: Market) => {
    setSelectedMarket(market);
    setIsFormOpen(true);
  };

  const handleCreate = () => {
    setSelectedMarket(null);
    setIsFormOpen(true);
  };

  const handleDeleteClick = (market: Market) => {
    setMarketToDelete(market);
    setShowDeleteConfirm(true);
  };

  const handleDelete = () => {
    if (!marketToDelete) return;

    deleteMarketMutation.mutate(marketToDelete.id, {
      onSuccess: () => {
        toast({
          title: "Market deleted",
          description: "The market has been successfully deleted.",
        });
        setMarketToDelete(null);
      },
      onError: (error: Error) => {
        toast({
          title: "Error deleting market",
          description: error.message,
          variant: "destructive",
        });
      },
    });
  };

  return (
    <div className="p-6 sm:p-10 bg-gradient-to-br from-background to-muted/20 min-h-screen">
      {/* Header Section */}
      <div className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 mb-8 shadow-lg shadow-black/5">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Market Management
            </h1>
            <p className="text-muted-foreground mt-2">
              Create, update, or delete your sales markets and configure their
              fee structures.
            </p>
          </div>
          <Button
            onClick={handleCreate}
            className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Create Market
          </Button>
        </div>
      </div>

      {/* Markets Table */}
      <div className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl shadow-lg shadow-black/5 overflow-hidden">
        <Table>
          <TableHeader className="bg-gray-800 border-b border-gray-700">
            <TableRow className="border-b-gray-700 hover:bg-gray-700/50">
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">
                Name
              </TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">
                Transaction Fee
              </TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">
                Processing Fee
              </TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">
                Other Fee
              </TableHead>
              <TableHead className="text-right text-gray-200 font-bold text-sm uppercase tracking-wider">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow className="bg-gray-900/50">
                <TableCell
                  colSpan={5}
                  className="text-center py-10 text-gray-400"
                >
                  Loading markets...
                </TableCell>
              </TableRow>
            ) : markets.length === 0 ? (
              <TableRow className="bg-gray-900/50">
                <TableCell
                  colSpan={5}
                  className="text-center py-10 text-gray-400"
                >
                  No markets found. Create one to get started.
                </TableCell>
              </TableRow>
            ) : (
              markets.map((market, index) => (
                <TableRow
                  key={market.id}
                  className={`border-b-gray-800 hover:bg-gray-700/30 transition-colors ${
                    index % 2 === 0 ? "bg-gray-900/30" : "bg-gray-800/30"
                  }`}
                >
                  <TableCell className="font-medium text-gray-100">
                    {market.name}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {formatFeeDisplay(
                      market.transaction_fee,
                      market.transaction_fee_type
                    )}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {formatFeeDisplay(
                      market.processing_fee,
                      market.processing_fee_type
                    )}
                  </TableCell>
                  <TableCell className="text-gray-300">
                    {formatFeeDisplay(market.other_fee, market.other_fee_type)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(market)}
                        className="text-blue-400 hover:text-blue-300 hover:bg-blue-400/10"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteClick(market)}
                        className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <MarketFormDialog
        isOpen={isFormOpen}
        setIsOpen={setIsFormOpen}
        market={selectedMarket}
      />

      <ConfirmDialog
        open={showDeleteConfirm}
        onOpenChange={setShowDeleteConfirm}
        onConfirm={handleDelete}
        title="Delete Market"
        description="Are you sure you want to delete this market? This action cannot be undone."
        confirmText="Delete"
        isLoading={deleteMarketMutation.isPending}
      />
    </div>
  );
};

export default MarketsPage;
