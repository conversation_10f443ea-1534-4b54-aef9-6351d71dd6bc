
import { useState } from "react";
import { MarketSelector } from "@/components/MarketSelector";
import { ProductPricingTable } from "@/components/ProductPricingTable";
import { ProductSearchDialog } from "@/components/ProductSearchDialog";
import { useMarket } from "@/hooks/useMarkets";
import { useTrackedProducts } from "@/hooks/useTrackedProducts";
import { EditPriceDialog } from "@/components/EditPriceDialog";

const PricingTool = () => {
  const [selectedMarket, setSelectedMarket] = useState("Etsy");
  const { data: marketData } = useMarket(selectedMarket);
  const { data: products = [], isLoading, refetch: refetchProducts } = useTrackedProducts();
  const [selectedProductIds, setSelectedProductIds] = useState<string[]>([]);

  const selectedProducts = products.filter(p => selectedProductIds.includes(p.id));

  const handleProductsAdded = () => {
    refetchProducts();
  };

  return (
    <div className="p-6 sm:p-10 bg-gradient-to-br from-background to-muted/20 min-h-screen">
      {/* Market Selection Card */}
      <div className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 mb-8 shadow-lg shadow-black/5">
        <h2 className="text-lg font-semibold text-foreground mb-4">Market Selection</h2>
        <MarketSelector 
          selectedMarket={selectedMarket}
          onMarketChange={setSelectedMarket}
          marketData={marketData}
        />
      </div>

      {/* Main Content */}
      <div className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-lg shadow-black/5">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <EditPriceDialog 
              selectedProducts={selectedProducts}
              market={marketData}
              onUpdate={() => setSelectedProductIds([])}
            />
          </div>
          <div className="flex items-center gap-3">
            <ProductSearchDialog onProductsAdded={handleProductsAdded} />
          </div>
        </div>

        <ProductPricingTable 
          selectedMarket={selectedMarket}
          market={marketData}
          products={products}
          isLoading={isLoading}
          selectedProducts={selectedProductIds}
          onSelectionChange={setSelectedProductIds}
        />
      </div>
    </div>
  );
};

export default PricingTool;
