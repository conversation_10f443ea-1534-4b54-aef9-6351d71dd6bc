import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { User, Palette, Key, CreditCard, Zap, Sun, Moon, Monitor } from 'lucide-react';
import { ApiKeyDialog } from '@/components/ApiKeyDialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { applyColorMode, applyFontSize } from '@/utils/themeUtils';

interface Profile {
  id: string;
  first_name: string | null;
  last_name: string | null;
  created_at: string;
  updated_at: string;
}

const Settings = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [colorMode, setColorMode] = useState(() => 
    localStorage.getItem('color-mode') || 'match-system'
  );
  const [fontSize, setFontSize] = useState(() => 
    localStorage.getItem('font-size') || 'default'
  );

  const form = useForm({
    defaultValues: {
      first_name: '',
      last_name: '',
    },
  });

  // Apply color mode changes
  useEffect(() => {
    applyColorMode(colorMode);
    localStorage.setItem('color-mode', colorMode);
  }, [colorMode]);

  // Apply font size changes
  useEffect(() => {
    applyFontSize(fontSize);
    localStorage.setItem('font-size', fontSize);
  }, [fontSize]);

  const getInitials = () => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name.charAt(0)}${profile.last_name.charAt(0)}`.toUpperCase();
    } else if (profile?.first_name) {
      return profile.first_name.charAt(0).toUpperCase();
    } else if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return "U";
  };

  const getDisplayName = () => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    } else if (profile?.first_name) {
      return profile.first_name;
    }
    return user?.email?.split('@')[0] || 'User';
  };

  useEffect(() => {
    if (user) {
      fetchProfile();
    }
  }, [user]);

  const fetchProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      if (data) {
        setProfile(data);
        form.reset({
          first_name: data.first_name || '',
          last_name: data.last_name || '',
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast({
        title: "Error",
        description: "Failed to load profile information",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (values: { first_name: string; last_name: string }) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user?.id,
          first_name: values.first_name || null,
          last_name: values.last_name || null,
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Profile updated successfully",
      });

      // Refresh profile data
      fetchProfile();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-full bg-background text-foreground">
      <Tabs defaultValue="profile" orientation="vertical" className="flex w-full">
        {/* Sidebar with tabs */}
        <div className="w-64 border-r border-border p-6">
          <h1 className="text-2xl font-bold mb-8">Settings</h1>
          <TabsList className="flex flex-col w-full h-auto bg-transparent p-0 space-y-2">
            <TabsTrigger 
              value="profile" 
              className="w-full justify-start px-4 py-3 text-left data-[state=active]:bg-primary/20 data-[state=active]:text-primary data-[state=active]:border-l-2 data-[state=active]:border-primary hover:bg-muted/50"
            >
              <User className="h-4 w-4 mr-3" />
              Profile
            </TabsTrigger>
            <TabsTrigger 
              value="appearance" 
              className="w-full justify-start px-4 py-3 text-left data-[state=active]:bg-primary/20 data-[state=active]:text-primary data-[state=active]:border-l-2 data-[state=active]:border-primary hover:bg-muted/50"
            >
              <Palette className="h-4 w-4 mr-3" />
              Appearance
            </TabsTrigger>
            <TabsTrigger 
              value="api" 
              className="w-full justify-start px-4 py-3 text-left data-[state=active]:bg-primary/20 data-[state=active]:text-primary data-[state=active]:border-l-2 data-[state=active]:border-primary hover:bg-muted/50"
            >
              <Key className="h-4 w-4 mr-3" />
              API
            </TabsTrigger>
            <TabsTrigger 
              value="billing" 
              className="w-full justify-start px-4 py-3 text-left data-[state=active]:bg-primary/20 data-[state=active]:text-primary data-[state=active]:border-l-2 data-[state=active]:border-primary hover:bg-muted/50"
            >
              <CreditCard className="h-4 w-4 mr-3" />
              Billing
            </TabsTrigger>
            <TabsTrigger 
              value="integrations" 
              className="w-full justify-start px-4 py-3 text-left data-[state=active]:bg-primary/20 data-[state=active]:text-primary data-[state=active]:border-l-2 data-[state=active]:border-primary hover:bg-muted/50"
            >
              <Zap className="h-4 w-4 mr-3" />
              Integrations
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Content area */}
        <div className="flex-1 p-8">
          <TabsContent value="profile" className="mt-0 h-full">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold mb-2">Profile</h2>
                <p className="text-muted-foreground mb-8">Manage your account profile information.</p>
              </div>
              
              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center gap-6 mb-8">
                  <Avatar className="h-20 w-20">
                    <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                      {getInitials()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="text-xl font-semibold">{getDisplayName()}</h3>
                    <p className="text-muted-foreground">{user?.email}</p>
                    <span className="inline-block bg-primary/20 text-primary px-3 py-1 rounded-full text-sm mt-2">
                      Free plan
                    </span>
                  </div>
                </div>

                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="first_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First name</FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="Enter your first name"
                                disabled={loading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="last_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last name</FormLabel>
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="Enter your last name"
                                disabled={loading}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="flex gap-4">
                      <Button 
                        type="submit" 
                        disabled={loading}
                      >
                        Save changes
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => form.reset()}
                        disabled={loading}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="mt-0 h-full">
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-semibold mb-2">Appearance</h2>
                <p className="text-muted-foreground mb-8">Customize how POD Tools looks for you.</p>
              </div>
              
              {/* Color Mode Section */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Color mode</h3>
                  <RadioGroup value={colorMode} onValueChange={setColorMode} className="grid grid-cols-3 gap-4">
                    <div className="relative">
                      <RadioGroupItem value="light" id="light" className="peer sr-only" />
                      <Label
                        htmlFor="light"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-4 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 flex h-16 w-20 items-center justify-center rounded-md bg-white border">
                          <Sun className="h-6 w-6 text-orange-500" />
                        </div>
                        <span className="text-sm font-medium">Light</span>
                      </Label>
                    </div>
                    
                    <div className="relative">
                      <RadioGroupItem value="match-system" id="match-system" className="peer sr-only" />
                      <Label
                        htmlFor="match-system"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-4 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 flex h-16 w-20 items-center justify-center rounded-md bg-gradient-to-r from-white to-gray-900 border">
                          <Monitor className="h-6 w-6 text-orange-500" />
                        </div>
                        <span className="text-sm font-medium">Match system</span>
                      </Label>
                    </div>
                    
                    <div className="relative">
                      <RadioGroupItem value="dark" id="dark" className="peer sr-only" />
                      <Label
                        htmlFor="dark"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-4 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 flex h-16 w-20 items-center justify-center rounded-md bg-gray-900 border">
                          <Moon className="h-6 w-6 text-orange-500" />
                        </div>
                        <span className="text-sm font-medium">Dark</span>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>

                {/* Font Size Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Font size</h3>
                  <RadioGroup value={fontSize} onValueChange={setFontSize} className="grid grid-cols-3 gap-4">
                    <div className="relative">
                      <RadioGroupItem value="small" id="small" className="peer sr-only" />
                      <Label
                        htmlFor="small"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-6 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 text-sm font-medium">Aa</div>
                        <span className="text-sm font-medium text-muted-foreground">Small</span>
                      </Label>
                    </div>
                    
                    <div className="relative">
                      <RadioGroupItem value="default" id="default" className="peer sr-only" />
                      <Label
                        htmlFor="default"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-6 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 text-lg font-medium">Aa</div>
                        <span className="text-sm font-medium text-muted-foreground">Default</span>
                      </Label>
                    </div>
                    
                    <div className="relative">
                      <RadioGroupItem value="large" id="large" className="peer sr-only" />
                      <Label
                        htmlFor="large"
                        className="flex flex-col items-center justify-center rounded-lg border-2 border-border bg-card p-6 hover:bg-muted/50 peer-data-[state=checked]:border-primary peer-data-[state=checked]:bg-primary/10 cursor-pointer transition-all"
                      >
                        <div className="mb-3 text-xl font-medium">Aa</div>
                        <span className="text-sm font-medium text-muted-foreground">Large</span>
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="api" className="mt-0 h-full">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold mb-2">API configuration</h2>
                <p className="text-muted-foreground mb-8">Manage your API keys and integrations.</p>
              </div>
              
              <div className="bg-card border border-border rounded-lg p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Printify API</h3>
                    <p className="text-muted-foreground text-sm mb-4">Configure your Printify API key to access product catalog and pricing information.</p>
                    <ApiKeyDialog variant="button" />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="billing" className="mt-0 h-full">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold mb-2">Billing</h2>
                <p className="text-muted-foreground mb-8">Manage your subscription and billing information.</p>
              </div>
              
              <div className="bg-card border border-border rounded-lg p-6">
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Free plan</h3>
                  <p className="text-muted-foreground mb-4">You're currently on the free plan.</p>
                  <Button>
                    Upgrade to Pro
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="integrations" className="mt-0 h-full">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold mb-2">Integrations</h2>
                <p className="text-muted-foreground mb-8">Connect with external services and tools.</p>
              </div>
              
              <div className="bg-card border border-border rounded-lg p-6">
                <p className="text-muted-foreground">More integrations coming soon...</p>
              </div>
            </div>
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default Settings;
