
export interface Market {
  id: string;
  name: string;
  transaction_fee: number;
  transaction_fee_type: 'percentage' | 'flat';
  processing_fee: number;
  processing_fee_type: 'percentage' | 'flat';
  other_fee: number;
  other_fee_type: 'percentage' | 'flat';
  created_at: string;
  updated_at: string;
}

export interface Blueprint {
  id: string;
  printify_id: number;
  title: string;
  description?: string;
  brand?: string;
  model?: string;
  created_at: string;
}

export interface PrintProvider {
  id: string;
  printify_id: number;
  title: string;
  location?: string;
  created_at: string;
}

export interface Country {
  id: string;
  name: string;
  code: string;
  created_at: string;
}

export interface ShippingMethod {
  id: string;
  name: string;
  code: string;
  created_at: string;
}

export interface VariantShippingCost {
  id: string;
  tracked_product_id: string;
  country_id: string;
  shipping_method_id: string;
  first_item_cost: number;
  additional_item_cost: number;
  delivery_time_min?: number;
  delivery_time_max?: number;
  created_at: string;
  updated_at: string;
}

// Partial types for joined data in queries
export interface BlueprintPartial {
  id: string;
  title: string;
  brand?: string;
  model?: string;
}

export interface PrintProviderPartial {
  id: string;
  title: string;
  location?: string;
}

export interface TrackedProduct {
  id: string;
  blueprint_id: string;
  print_provider_id: string;
  printify_variant_id: number;
  variant_title: string;
  size?: string;
  color?: string;
  production_cost: number;
  shipping_cost: number;
  discount_percentage: number;
  profit_margin_percentage: number;
  retail_price?: number;
  is_active: boolean;
  notes?: string;
  offers_free_shipping: boolean;
  default_shipping_country_id?: string;
  default_shipping_method_id?: string;
  created_at: string;
  updated_at: string;
  blueprints?: BlueprintPartial;
  print_providers?: PrintProviderPartial;
}
