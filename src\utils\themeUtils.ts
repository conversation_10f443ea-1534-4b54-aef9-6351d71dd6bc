
export const applyColorMode = (colorMode: string) => {
  const root = document.documentElement;
  
  if (colorMode === 'light') {
    root.classList.remove('dark');
  } else if (colorMode === 'dark') {
    root.classList.add('dark');
  } else if (colorMode === 'match-system') {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }
};

export const applyFontSize = (fontSize: string) => {
  const root = document.documentElement;
  root.classList.remove('font-size-small', 'font-size-default', 'font-size-large');
  root.classList.add(`font-size-${fontSize}`);
};

export const initializeTheme = () => {
  // Initialize color mode
  const savedColorMode = localStorage.getItem('color-mode') || 'match-system';
  applyColorMode(savedColorMode);

  // Initialize font size
  const savedFontSize = localStorage.getItem('font-size') || 'default';
  applyFontSize(savedFontSize);

  // Listen for system theme changes when in match-system mode
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleSystemThemeChange = () => {
    const currentMode = localStorage.getItem('color-mode') || 'match-system';
    if (currentMode === 'match-system') {
      applyColorMode('match-system');
    }
  };
  
  mediaQuery.addEventListener('change', handleSystemThemeChange);
  
  return () => {
    mediaQuery.removeEventListener('change', handleSystemThemeChange);
  };
};
