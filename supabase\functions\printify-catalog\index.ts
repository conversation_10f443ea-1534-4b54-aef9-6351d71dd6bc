import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY")!;
    const supabase = createClient(supabaseUrl, supabaseKey, {
      global: { headers: { Authorization: req.headers.get("Authorization")! } },
    });

    // Get the authenticated user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      throw new Error("Authentication required");
    }

    // Get user's Printify API key
    const { data: apiKeyData, error: keyError } = await supabase
      .from("user_api_keys")
      .select("api_key_encrypted")
      .eq("service_name", "printify")
      .single();

    if (keyError || !apiKeyData?.api_key_encrypted) {
      throw new Error(
        "Printify API key not configured. Please add your API key in settings."
      );
    }

    const printifyApiKey = apiKeyData.api_key_encrypted;
    console.log(
      "Using API key (first 10 chars):",
      printifyApiKey.substring(0, 10) + "..."
    );

    const {
      action,
      search,
      blueprintId,
      printProviderId,
      storeId,
      variantIds,
      productId,
      countryCode = "US",
      trackedProductIds,
    } = await req.json();

    const headers = {
      Authorization: `Bearer ${printifyApiKey}`,
      "Content-Type": "application/json",
    };

    let response;

    switch (action) {
      case "get-stores": {
        console.log("Fetching Printify stores");
        response = await fetch("https://api.printify.com/v1/shops.json", {
          headers,
          method: "GET",
        });
        break;
      }

      case "save-all-shipping-costs": {
        if (
          !trackedProductIds ||
          !Array.isArray(trackedProductIds) ||
          trackedProductIds.length === 0
        ) {
          throw new Error("Tracked product IDs are required");
        }

        console.log(
          `Saving shipping costs for ${trackedProductIds.length} products`
        );

        // Get all tracked products with their Printify variant IDs
        const { data: trackedProducts, error: productsError } = await supabase
          .from("tracked_products")
          .select(
            "id, printify_variant_id, blueprint_id, print_provider_id, blueprints!inner(printify_id), print_providers!inner(printify_id)"
          )
          .in("id", trackedProductIds);

        if (productsError || !trackedProducts) {
          console.error("Error fetching tracked products:", productsError);
          throw new Error("Failed to fetch tracked products");
        }

        console.log(`Found ${trackedProducts.length} tracked products:`);
        trackedProducts.forEach((p) => {
          console.log(
            `- Product ${p.id}: Variant ${p.printify_variant_id}, Blueprint ${p.blueprints.printify_id}, Provider ${p.print_providers.printify_id}`
          );
        });

        // Get countries and shipping methods for mapping
        const { data: countries } = await supabase
          .from("countries")
          .select("id, code, name");
        const { data: shippingMethods } = await supabase
          .from("shipping_methods")
          .select("id, code, name");

        if (!countries || !shippingMethods) {
          throw new Error("Failed to fetch countries or shipping methods");
        }

        // Group products by blueprint and print provider to minimize API calls
        const productGroups = new Map();
        for (const product of trackedProducts) {
          const key = `${product.blueprints.printify_id}-${product.print_providers.printify_id}`;
          if (!productGroups.has(key)) {
            productGroups.set(key, {
              blueprintId: product.blueprints.printify_id,
              printProviderId: product.print_providers.printify_id,
              products: [],
            });
          }
          productGroups.get(key).products.push(product);
        }

        console.log(
          `Processing ${productGroups.size} blueprint-provider combinations`
        );

        for (const [key, group] of productGroups) {
          console.log(
            `Processing group: Blueprint ${group.blueprintId}, Provider ${group.printProviderId}`
          );

          const allShippingCosts = [];
          let usingV2 = true;

          try {
            // Step 1: Get available shipping methods using V2 API
            const v2ShippingUrl = `https://api.printify.com/v2/catalog/blueprints/${group.blueprintId}/print_providers/${group.printProviderId}/shipping.json`;
            console.log(
              `Fetching available shipping methods from: ${v2ShippingUrl}`
            );

            const v2ShippingResponse = await fetch(v2ShippingUrl, { headers });

            if (v2ShippingResponse.ok) {
              const v2ShippingData = await v2ShippingResponse.json();
              console.log(
                `V2 methods response for blueprint ${group.blueprintId}:`,
                JSON.stringify(v2ShippingData, null, 2)
              );

              // Check if we have links to method-specific endpoints
              if (
                v2ShippingData.links &&
                typeof v2ShippingData.links === "object"
              ) {
                const methodLinks = v2ShippingData.links;
                console.log(
                  `Found ${
                    Object.keys(methodLinks).length
                  } shipping method links:`,
                  Object.keys(methodLinks)
                );

                // Step 2: For each method link, fetch detailed shipping costs
                for (const [methodName, methodUrl] of Object.entries(
                  methodLinks
                )) {
                  console.log(
                    `Fetching ${methodName} shipping costs from: ${methodUrl}`
                  );

                  try {
                    const methodResponse = await fetch(methodUrl as string, {
                      headers,
                    });

                    if (methodResponse.ok) {
                      const methodData = await methodResponse.json();
                      console.log(
                        `${methodName} method response data length:`,
                        methodData.data?.length || 0
                      );
                      console.log(
                        `${methodName} method full response:`,
                        JSON.stringify(methodData, null, 2)
                      );

                      // Check if method returns valid data
                      if (
                        methodData.data &&
                        Array.isArray(methodData.data) &&
                        methodData.data.length > 0
                      ) {
                        console.log(
                          `Processing ${methodData.data.length} shipping records for ${methodName} method`
                        );

                        for (const shippingItem of methodData.data) {
                          if (
                            shippingItem.attributes &&
                            shippingItem.attributes.variantId
                          ) {
                            const variantId = shippingItem.attributes.variantId;
                            const countryCode =
                              shippingItem.attributes.country?.code;
                            const shippingType =
                              shippingItem.attributes.shippingType;
                            const cost =
                              shippingItem.attributes.shippingCost?.firstItem
                                ?.amount;
                            const additionalCost =
                              shippingItem.attributes.shippingCost
                                ?.additionalItems?.amount;
                            const deliveryTimeMin =
                              shippingItem.attributes.handlingTime?.from;
                            const deliveryTimeMax =
                              shippingItem.attributes.handlingTime?.to;

                            if (cost && countryCode && shippingType) {
                              // Find matching tracked product
                              const trackedProduct = group.products.find(
                                (p) => p.printify_variant_id === variantId
                              );
                              if (trackedProduct) {
                                // Find matching country and shipping method
                                const country = countries.find(
                                  (c) => c.code === countryCode
                                );
                                const method = shippingMethods.find(
                                  (m) => m.code === shippingType.toLowerCase()
                                );

                                if (country && method) {
                                  allShippingCosts.push({
                                    tracked_product_id: trackedProduct.id,
                                    country_id: country.id,
                                    shipping_method_id: method.id,
                                    first_item_cost: cost / 100, // Convert cents to dollars
                                    additional_item_cost: additionalCost
                                      ? additionalCost / 100
                                      : 0,
                                    delivery_time_min: deliveryTimeMin,
                                    delivery_time_max: deliveryTimeMax,
                                  });
                                } else {
                                  console.log(
                                    `Mapping not found - Country: ${countryCode}, Method: ${shippingType}`
                                  );
                                }
                              }
                            }
                          }
                        }
                      } else {
                        console.log(
                          `${methodName} method returned empty data - method not available`
                        );
                      }
                    } else {
                      console.log(
                        `Failed to fetch ${methodName} method: ${methodResponse.status}`
                      );
                    }
                  } catch (methodError) {
                    console.log(
                      `Error fetching ${methodName} method:`,
                      methodError.message
                    );
                  }
                }
              } else {
                console.log("V2 response missing links, falling back to V1");
                usingV2 = false;
              }
            } else {
              console.log(
                `V2 shipping failed: ${v2ShippingResponse.status}, falling back to V1`
              );
              usingV2 = false;
            }
          } catch (v2Error) {
            console.log("V2 API failed, falling back to V1:", v2Error.message);
            usingV2 = false;
          }

          // Fallback to V1 API if V2 failed or returned no useful data
          if (!usingV2 || allShippingCosts.length === 0) {
            console.log(
              "Using V1 API fallback for blueprint",
              group.blueprintId
            );

            try {
              const v1ShippingUrl = `https://api.printify.com/v1/catalog/blueprints/${group.blueprintId}/print_providers/${group.printProviderId}/shipping.json`;
              const v1ShippingResponse = await fetch(v1ShippingUrl, {
                headers,
              });

              if (v1ShippingResponse.ok) {
                const v1ShippingData = await v1ShippingResponse.json();
                console.log(
                  `V1 shipping data received for blueprint ${group.blueprintId}`
                );

                if (
                  v1ShippingData.profiles &&
                  Array.isArray(v1ShippingData.profiles)
                ) {
                  for (const profile of v1ShippingData.profiles) {
                    if (
                      profile.variant_ids &&
                      Array.isArray(profile.variant_ids) &&
                      profile.countries
                    ) {
                      for (const variantId of profile.variant_ids) {
                        for (const countryCode of profile.countries) {
                          // Find matching tracked product
                          const trackedProduct = group.products.find(
                            (p) => p.printify_variant_id === variantId
                          );
                          if (trackedProduct) {
                            // Find matching country (use "standard" as default method for V1)
                            const country = countries.find(
                              (c) => c.code === countryCode
                            );
                            const method = shippingMethods.find(
                              (m) => m.code === "standard"
                            );

                            if (country && method && profile.first_item?.cost) {
                              allShippingCosts.push({
                                tracked_product_id: trackedProduct.id,
                                country_id: country.id,
                                shipping_method_id: method.id,
                                first_item_cost: profile.first_item.cost / 100, // Convert cents to dollars
                                additional_item_cost: profile.additional_items
                                  ?.cost
                                  ? profile.additional_items.cost / 100
                                  : 0,
                                delivery_time_min:
                                  v1ShippingData.handling_time?.value || null,
                                delivery_time_max:
                                  v1ShippingData.handling_time?.value || null,
                              });
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            } catch (v1Error) {
              console.error(
                `V1 API also failed for blueprint ${group.blueprintId}:`,
                v1Error.message
              );
            }
          }

          // Save shipping costs to database
          if (allShippingCosts.length > 0) {
            console.log(
              `Saving ${allShippingCosts.length} shipping cost records for blueprint ${group.blueprintId}`
            );
            console.log(
              `Shipping costs to save:`,
              JSON.stringify(allShippingCosts, null, 2)
            );

            // Use a proper merge approach: delete all existing records for these products, then insert new ones
            console.log(
              `Merging ${allShippingCosts.length} shipping cost records for blueprint ${group.blueprintId}`
            );

            const productIds = group.products.map((p) => p.id);
            console.log(
              `Deleting all existing shipping costs for products:`,
              productIds
            );

            // First, let's see what records exist before deletion
            const { data: existingRecords, error: selectError } = await supabase
              .from("variant_shipping_costs")
              .select("id, tracked_product_id, country_id, shipping_method_id")
              .in("tracked_product_id", productIds);

            if (selectError) {
              console.error("Error checking existing records:", selectError);
            } else {
              console.log(
                `Found ${
                  existingRecords?.length || 0
                } existing records to delete:`,
                existingRecords
              );
            }

            const { error: deleteError, count: deletedCount } = await supabase
              .from("variant_shipping_costs")
              .delete({ count: "exact" })
              .in("tracked_product_id", productIds);

            if (deleteError) {
              console.error(
                "Error deleting existing shipping costs:",
                deleteError
              );
            } else {
              console.log(
                `Successfully deleted ${
                  deletedCount || 0
                } existing shipping cost records`
              );
            }

            // Verify deletion worked
            const { data: remainingRecords, error: verifyError } =
              await supabase
                .from("variant_shipping_costs")
                .select(
                  "id, tracked_product_id, country_id, shipping_method_id"
                )
                .in("tracked_product_id", productIds);

            if (verifyError) {
              console.error("Error verifying deletion:", verifyError);
            } else {
              console.log(
                `Remaining records after deletion: ${
                  remainingRecords?.length || 0
                }`,
                remainingRecords
              );
            }

            // Add a small delay to ensure deletion is committed
            await new Promise((resolve) => setTimeout(resolve, 100));

            // Insert records one by one to handle any remaining duplicates gracefully
            console.log(
              `Inserting ${allShippingCosts.length} new shipping cost records one by one`
            );

            let successCount = 0;
            let errorCount = 0;

            for (const cost of allShippingCosts) {
              const { error: insertError } = await supabase
                .from("variant_shipping_costs")
                .insert([cost]);

              if (insertError) {
                if (insertError.code === "23505") {
                  // Duplicate key error - try to update the existing record instead
                  console.log(
                    `Duplicate found for product ${cost.tracked_product_id}, attempting update...`
                  );
                  const { error: updateError } = await supabase
                    .from("variant_shipping_costs")
                    .update({
                      first_item_cost: cost.first_item_cost,
                      additional_item_cost: cost.additional_item_cost,
                      delivery_time_min: cost.delivery_time_min,
                      delivery_time_max: cost.delivery_time_max,
                    })
                    .eq("tracked_product_id", cost.tracked_product_id)
                    .eq("country_id", cost.country_id)
                    .eq("shipping_method_id", cost.shipping_method_id);

                  if (updateError) {
                    console.error(
                      "Error updating existing record:",
                      updateError
                    );
                    errorCount++;
                  } else {
                    console.log("Successfully updated existing record");
                    successCount++;
                  }
                } else {
                  console.error("Error inserting shipping cost:", insertError);
                  errorCount++;
                }
              } else {
                successCount++;
              }
            }

            console.log(
              `Completed processing: ${successCount} successful, ${errorCount} errors for blueprint ${group.blueprintId}`
            );
          } else {
            console.log(
              `No shipping costs found for blueprint ${group.blueprintId}. Reasons could be:`
            );
            console.log(`- V2 API failed: ${!usingV2}`);
            console.log(
              `- V2 API returned no data: ${
                usingV2 && allShippingCosts.length === 0
              }`
            );
            console.log(`- No matching countries/methods found in database`);
            console.log(`- Printify API returned empty shipping data`);
          }
        }

        return new Response(
          JSON.stringify({
            success: true,
            message: `Processed shipping costs for ${trackedProducts.length} products using V2 and V1 APIs`,
          }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      case "get-shipping-costs": {
        if (!blueprintId || !printProviderId) {
          throw new Error(
            "Blueprint ID and Print Provider ID are required for shipping costs"
          );
        }

        console.log(
          `Fetching shipping costs for blueprint ${blueprintId} and provider ${printProviderId}`
        );

        // Use Catalog V2 for shipping costs
        const shippingUrl = `https://api.printify.com/v2/catalog/blueprints/${blueprintId}/print_providers/${printProviderId}/shipping.json?country=${countryCode}`;

        console.log("Shipping URL:", shippingUrl);

        response = await fetch(shippingUrl, {
          headers,
          method: "GET",
        });

        if (!response.ok) {
          console.log("V2 shipping failed, trying V1...");
          // Fallback to V1 if V2 fails
          const v1ShippingUrl = `https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers/${printProviderId}/shipping.json`;
          response = await fetch(v1ShippingUrl, {
            headers,
            method: "GET",
          });
        }
        break;
      }

      case "save-shipping-costs": {
        const { trackedProductId, shippingCosts } = await req.json();

        if (!trackedProductId || !shippingCosts) {
          throw new Error("Tracked product ID and shipping costs are required");
        }

        console.log(`Saving shipping costs for product ${trackedProductId}`);

        // First, delete existing shipping costs for this product
        await supabase
          .from("variant_shipping_costs")
          .delete()
          .eq("tracked_product_id", trackedProductId);

        // Insert new shipping costs
        const insertData = shippingCosts.map((cost: any) => ({
          tracked_product_id: trackedProductId,
          country_id: cost.country_id,
          shipping_method_id: cost.shipping_method_id,
          first_item_cost: cost.first_item_cost,
          additional_item_cost: cost.additional_item_cost || 0,
          delivery_time_min: cost.delivery_time_min,
          delivery_time_max: cost.delivery_time_max,
        }));

        const { error: insertError } = await supabase
          .from("variant_shipping_costs")
          .insert(insertData);

        if (insertError) {
          console.error("Error saving shipping costs:", insertError);
          throw new Error("Failed to save shipping costs to database");
        }

        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }

      case "upload-placeholder-image": {
        console.log("Uploading placeholder image to Printify");

        // Create a simple 1x1 pixel transparent PNG as base64
        const placeholderImageBase64 =
          "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

        const imageData = {
          file_name: "placeholder.png",
          contents: placeholderImageBase64,
        };

        response = await fetch(
          "https://api.printify.com/v1/uploads/images.json",
          {
            method: "POST",
            headers,
            body: JSON.stringify(imageData),
          }
        );
        break;
      }

      case "create-placeholder-product": {
        if (
          !storeId ||
          !blueprintId ||
          !printProviderId ||
          !variantIds ||
          variantIds.length === 0
        ) {
          throw new Error(
            "Store ID, Blueprint ID, Print Provider ID, and Variant IDs are required"
          );
        }

        console.log("Creating placeholder product for cost calculation");

        // First, upload a placeholder image
        console.log("Uploading placeholder image...");
        const placeholderImageBase64 =
          "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

        const imageUploadResponse = await fetch(
          "https://api.printify.com/v1/uploads/images.json",
          {
            method: "POST",
            headers,
            body: JSON.stringify({
              file_name: "placeholder.png",
              contents: placeholderImageBase64,
            }),
          }
        );

        if (!imageUploadResponse.ok) {
          const errorText = await imageUploadResponse.text();
          console.error(
            "Image upload failed:",
            imageUploadResponse.status,
            errorText
          );
          throw new Error(
            `Failed to upload placeholder image: ${imageUploadResponse.statusText}`
          );
        }

        const uploadedImage = await imageUploadResponse.json();
        console.log("Uploaded image ID:", uploadedImage.id);

        // Now create the product with the uploaded image
        const productData = {
          title: `Cost Calculator - ${Date.now()}`,
          description: "Temporary product for cost calculation",
          blueprint_id: blueprintId,
          print_provider_id: printProviderId,
          variants: variantIds.map((variantId: number) => ({
            id: variantId,
            price: 1000, // $10.00 placeholder price
            is_enabled: true,
          })),
          print_areas: [
            {
              variant_ids: variantIds,
              placeholders: [
                {
                  position: "front",
                  images: [
                    {
                      id: uploadedImage.id,
                      x: 0.5,
                      y: 0.5,
                      scale: 0.1,
                      angle: 0,
                    },
                  ],
                },
              ],
            },
          ],
        };

        console.log("Product data:", JSON.stringify(productData, null, 2));

        response = await fetch(
          `https://api.printify.com/v1/shops/${storeId}/products.json`,
          {
            method: "POST",
            headers,
            body: JSON.stringify(productData),
          }
        );
        break;
      }

      case "get-product-costs": {
        if (!storeId || !productId) {
          throw new Error("Store ID and Product ID are required");
        }

        console.log("Fetching product costs for product:", productId);
        response = await fetch(
          `https://api.printify.com/v1/shops/${storeId}/products/${productId}.json`,
          {
            headers,
            method: "GET",
          }
        );
        break;
      }

      case "delete-placeholder-product": {
        if (!storeId || !productId) {
          throw new Error("Store ID and Product ID are required");
        }

        console.log("Deleting placeholder product:", productId);
        response = await fetch(
          `https://api.printify.com/v1/shops/${storeId}/products/${productId}.json`,
          {
            method: "DELETE",
            headers,
          }
        );
        break;
      }

      case "search-blueprints": {
        if (!search) throw new Error("Search term required");

        console.log("Searching for blueprints with term:", search);
        console.log(
          "Making request to:",
          "https://api.printify.com/v1/catalog/blueprints.json"
        );

        // First get all blueprints
        const allBlueprintsResponse = await fetch(
          "https://api.printify.com/v1/catalog/blueprints.json",
          {
            headers,
            method: "GET",
          }
        );

        console.log("Response status:", allBlueprintsResponse.status);
        console.log("Response status text:", allBlueprintsResponse.statusText);

        if (!allBlueprintsResponse.ok) {
          const errorText = await allBlueprintsResponse.text();
          console.error("Printify API error response:", errorText);
          throw new Error(
            `Printify API error: ${allBlueprintsResponse.status} ${allBlueprintsResponse.statusText} - ${errorText}`
          );
        }

        const responseText = await allBlueprintsResponse.text();
        console.log(
          "Raw response (first 500 chars):",
          responseText.substring(0, 500)
        );

        let allBlueprints;
        try {
          allBlueprints = JSON.parse(responseText);
        } catch (parseError) {
          console.error("Failed to parse JSON response:", parseError);
          throw new Error("Invalid JSON response from Printify API");
        }

        console.log("Response is array:", Array.isArray(allBlueprints));
        console.log(
          "Total blueprints received:",
          Array.isArray(allBlueprints)
            ? allBlueprints.length
            : allBlueprints.data?.length || 0
        );

        // Handle both possible response structures
        let blueprintsArray;
        if (Array.isArray(allBlueprints)) {
          // Direct array response
          blueprintsArray = allBlueprints;
        } else if (allBlueprints.data && Array.isArray(allBlueprints.data)) {
          // Object with data property
          blueprintsArray = allBlueprints.data;
        } else {
          console.log(
            "Unexpected response structure:",
            JSON.stringify(allBlueprints, null, 2)
          );
          throw new Error("Unexpected response structure from Printify API");
        }

        // Log first few blueprints to see structure
        if (blueprintsArray.length > 0) {
          console.log(
            "Sample blueprint structure:",
            JSON.stringify(blueprintsArray[0], null, 2)
          );
        }

        // Filter blueprints based on search term with more flexible matching
        const searchTerm = search.toLowerCase().trim();
        const filteredBlueprints = blueprintsArray.filter((blueprint: any) => {
          const title = (blueprint.title || "").toLowerCase();
          const description = (blueprint.description || "").toLowerCase();
          const brand = (blueprint.brand || "").toLowerCase();
          const model = (blueprint.model || "").toLowerCase();
          const tags = Array.isArray(blueprint.tags)
            ? blueprint.tags.join(" ").toLowerCase()
            : "";

          // Create searchable text from all fields
          const searchableText =
            `${title} ${description} ${brand} ${model} ${tags}`.toLowerCase();

          console.log(
            `Checking blueprint: "${title}" against search term: "${searchTerm}"`
          );

          const matches = searchableText.includes(searchTerm);
          if (matches) {
            console.log(`✓ Match found: ${title}`);
          }

          return matches;
        });

        console.log("Filtered blueprints count:", filteredBlueprints.length);

        // If no matches found, try partial matching with individual words
        if (filteredBlueprints.length === 0) {
          console.log("No exact matches, trying partial matching...");
          const searchWords = searchTerm
            .split(" ")
            .filter((word) => word.length > 2);

          const partialMatches = blueprintsArray.filter((blueprint: any) => {
            const title = (blueprint.title || "").toLowerCase();
            const description = (blueprint.description || "").toLowerCase();

            return searchWords.some(
              (word) => title.includes(word) || description.includes(word)
            );
          });

          console.log("Partial matches found:", partialMatches.length);

          // Return filtered results in the same format as the original API
          return new Response(
            JSON.stringify({
              data: partialMatches.slice(0, 50), // Limit to 50 results
            }),
            {
              headers: { ...corsHeaders, "Content-Type": "application/json" },
            }
          );
        }

        // Return filtered results in the same format as the original API
        return new Response(
          JSON.stringify({
            data: filteredBlueprints.slice(0, 50), // Limit to 50 results
          }),
          {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      case "get-blueprint-providers": {
        if (!blueprintId) throw new Error("Blueprint ID required");
        response = await fetch(
          `https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers.json`,
          {
            headers,
          }
        );
        break;
      }

      case "get-blueprint-variants": {
        if (!blueprintId || !printProviderId)
          throw new Error("Blueprint ID and Print Provider ID required");

        // Get variants
        const variantsResponse = await fetch(
          `https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers/${printProviderId}/variants.json`,
          {
            headers,
          }
        );

        if (!variantsResponse.ok) {
          const errorText = await variantsResponse.text();
          console.error(
            "Variants API error:",
            variantsResponse.status,
            errorText
          );
          throw new Error(
            `Failed to fetch variants: ${variantsResponse.statusText}`
          );
        }

        const variantsData = await variantsResponse.json();
        console.log("Variants data received:", variantsData);

        return new Response(JSON.stringify(variantsData), {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        });
      }

      default:
        throw new Error("Invalid action");
    }

    if (response && !response.ok) {
      const errorText = await response.text();
      console.error("Printify API error:", response.status, errorText);

      if (response.status === 401) {
        throw new Error(
          "Invalid Printify API key. Please check your API key in settings."
        );
      } else if (response.status === 403) {
        throw new Error(
          "Access denied. Please verify your Printify API key permissions."
        );
      } else {
        throw new Error(`Printify API error: ${response.statusText}`);
      }
    }

    if (response) {
      const data = await response.json();
      return new Response(JSON.stringify(data), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }
  } catch (error) {
    console.error("Function error:", error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  }
});
