
-- Drop existing tables to recreate with better structure
DROP TABLE IF EXISTS public.products CASCADE;
DROP TABLE IF EXISTS public.marketplaces CASCADE;

-- Create markets table (renamed from marketplaces) with fee type support
CREATE TABLE public.markets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  transaction_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
  transaction_fee_type TEXT NOT NULL DEFAULT 'percentage' CHECK (transaction_fee_type IN ('percentage', 'flat')),
  processing_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
  processing_fee_type TEXT NOT NULL DEFAULT 'percentage' CHECK (processing_fee_type IN ('percentage', 'flat')),
  other_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
  other_fee_type TEXT NOT NULL DEFAULT 'flat' CHECK (other_fee_type IN ('percentage', 'flat')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create print_providers table to store Printify print providers we're tracking
CREATE TABLE public.print_providers (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  printify_id INTEGER NOT NULL UNIQUE,
  title TEXT NOT NULL,
  location TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create blueprints table to store Printify blueprints we're tracking
CREATE TABLE public.blueprints (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  printify_id INTEGER NOT NULL UNIQUE,
  title TEXT NOT NULL,
  description TEXT,
  brand TEXT,
  model TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create tracked_products table for products we want to track from Printify
CREATE TABLE public.tracked_products (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  blueprint_id UUID REFERENCES public.blueprints(id) ON DELETE CASCADE,
  print_provider_id UUID REFERENCES public.print_providers(id) ON DELETE CASCADE,
  printify_variant_id INTEGER NOT NULL,
  variant_title TEXT NOT NULL,
  size TEXT,
  color TEXT,
  -- Cost data from Printify
  production_cost DECIMAL(10,2) NOT NULL,
  shipping_cost DECIMAL(10,2) NOT NULL,
  -- Our pricing calculations
  discount_percentage INTEGER DEFAULT 0,
  profit_margin_percentage INTEGER DEFAULT 0,
  retail_price DECIMAL(10,2),
  -- Metadata
  is_active BOOLEAN DEFAULT true,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(blueprint_id, print_provider_id, printify_variant_id)
);

-- Insert default markets with proper fee types
INSERT INTO public.markets (name, transaction_fee, transaction_fee_type, processing_fee, processing_fee_type, other_fee, other_fee_type) VALUES
('Etsy', 6.5, 'percentage', 3.0, 'percentage', 0.45, 'flat'),
('Shopify', 2.9, 'percentage', 0.30, 'flat', 0.0, 'flat'),
('Amazon', 15.0, 'percentage', 0.0, 'flat', 0.99, 'flat'),
('Walmart', 15.0, 'percentage', 2.9, 'percentage', 0.0, 'flat'),
('eBay', 10.0, 'percentage', 2.9, 'percentage', 0.30, 'flat'),
('Facebook Marketplace', 5.0, 'percentage', 2.9, 'percentage', 0.0, 'flat');

-- Add indexes for better performance
CREATE INDEX idx_tracked_products_blueprint ON public.tracked_products(blueprint_id);
CREATE INDEX idx_tracked_products_provider ON public.tracked_products(print_provider_id);
CREATE INDEX idx_tracked_products_active ON public.tracked_products(is_active);
CREATE INDEX idx_blueprints_printify_id ON public.blueprints(printify_id);
CREATE INDEX idx_print_providers_printify_id ON public.print_providers(printify_id);

-- Enable Row Level Security (RLS) - making tables public for now since there's no authentication
ALTER TABLE public.markets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blueprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.print_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tracked_products ENABLE ROW LEVEL SECURITY;

-- Create policies to allow public access (since there's no authentication yet)
CREATE POLICY "Allow public access on markets" ON public.markets FOR ALL USING (true);
CREATE POLICY "Allow public access on blueprints" ON public.blueprints FOR ALL USING (true);
CREATE POLICY "Allow public access on print_providers" ON public.print_providers FOR ALL USING (true);
CREATE POLICY "Allow public access on tracked_products" ON public.tracked_products FOR ALL USING (true);
