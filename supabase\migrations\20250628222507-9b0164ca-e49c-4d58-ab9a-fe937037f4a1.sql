
-- First, let's add some sample countries (common Printify destinations)
-- We'll use INSERT with WHERE NOT EXISTS to avoid duplicates
INSERT INTO public.countries (name, code) 
SELECT 'United States', 'US' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'US')
UNION ALL
SELECT 'Canada', 'CA' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'CA')
UNION ALL
SELECT 'United Kingdom', 'GB' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'GB')
UNION ALL
SELECT 'Germany', 'DE' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'DE')
UNION ALL
SELECT 'France', 'FR' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'FR')
UNION ALL
SELECT 'Australia', 'AU' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'AU')
UNION ALL
SELECT 'Japan', 'JP' WHERE NOT EXISTS (SELECT 1 FROM public.countries WHERE code = 'JP');

-- Add some common shipping methods that Printify uses
INSERT INTO public.shipping_methods (name, code) 
SELECT 'Economy', 'economy' WHERE NOT EXISTS (SELECT 1 FROM public.shipping_methods WHERE code = 'economy')
UNION ALL
SELECT 'Standard', 'standard' WHERE NOT EXISTS (SELECT 1 FROM public.shipping_methods WHERE code = 'standard')
UNION ALL
SELECT 'Express', 'express' WHERE NOT EXISTS (SELECT 1 FROM public.shipping_methods WHERE code = 'express')
UNION ALL
SELECT 'Priority', 'priority' WHERE NOT EXISTS (SELECT 1 FROM public.shipping_methods WHERE code = 'priority');

-- Update tracked_products table to ensure it has the proper default values
-- We'll set US as default country and standard as default shipping method for existing products
UPDATE public.tracked_products 
SET 
  default_shipping_country_id = (SELECT id FROM public.countries WHERE code = 'US' LIMIT 1),
  default_shipping_method_id = (SELECT id FROM public.shipping_methods WHERE code = 'standard' LIMIT 1)
WHERE default_shipping_country_id IS NULL OR default_shipping_method_id IS NULL;

-- Create some sample shipping costs for existing tracked products
-- This is just sample data - in reality this would come from Printify API
INSERT INTO public.variant_shipping_costs (
  tracked_product_id, 
  country_id, 
  shipping_method_id, 
  first_item_cost, 
  additional_item_cost,
  delivery_time_min,
  delivery_time_max
)
SELECT 
  tp.id as tracked_product_id,
  c.id as country_id,
  sm.id as shipping_method_id,
  CASE 
    WHEN sm.code = 'economy' THEN tp.shipping_cost * 0.8
    WHEN sm.code = 'standard' THEN tp.shipping_cost
    WHEN sm.code = 'express' THEN tp.shipping_cost * 1.5
    WHEN sm.code = 'priority' THEN tp.shipping_cost * 2.0
    ELSE tp.shipping_cost
  END as first_item_cost,
  1.00 as additional_item_cost,
  CASE 
    WHEN sm.code = 'economy' THEN 7
    WHEN sm.code = 'standard' THEN 5
    WHEN sm.code = 'express' THEN 2
    WHEN sm.code = 'priority' THEN 1
    ELSE 5
  END as delivery_time_min,
  CASE 
    WHEN sm.code = 'economy' THEN 14
    WHEN sm.code = 'standard' THEN 10
    WHEN sm.code = 'express' THEN 5
    WHEN sm.code = 'priority' THEN 3
    ELSE 10
  END as delivery_time_max
FROM public.tracked_products tp
CROSS JOIN public.countries c
CROSS JOIN public.shipping_methods sm
WHERE tp.shipping_cost IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 FROM public.variant_shipping_costs vsc 
    WHERE vsc.tracked_product_id = tp.id 
    AND vsc.country_id = c.id 
    AND vsc.shipping_method_id = sm.id
  );
