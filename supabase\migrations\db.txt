-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.blueprints (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  printify_id integer NOT NULL UNIQUE,
  title text NOT NULL,
  description text,
  brand text,
  model text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT blueprints_pkey PRIMARY KEY (id)
);
CREATE TABLE public.countries (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  code text NOT NULL UNIQUE,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT countries_pkey PRIMARY KEY (id)
);
CREATE TABLE public.markets (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  transaction_fee numeric NOT NULL DEFAULT 0,
  transaction_fee_type text NOT NULL DEFAULT 'percentage'::text CHECK (transaction_fee_type = ANY (ARRAY['percentage'::text, 'flat'::text])),
  processing_fee numeric NOT NULL DEFAULT 0,
  processing_fee_type text NOT NULL DEFAULT 'percentage'::text CHECK (processing_fee_type = ANY (ARRAY['percentage'::text, 'flat'::text])),
  other_fee numeric NOT NULL DEFAULT 0,
  other_fee_type text NOT NULL DEFAULT 'flat'::text CHECK (other_fee_type = ANY (ARRAY['percentage'::text, 'flat'::text])),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT markets_pkey PRIMARY KEY (id)
);
CREATE TABLE public.print_providers (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  printify_id integer NOT NULL UNIQUE,
  title text NOT NULL,
  location text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT print_providers_pkey PRIMARY KEY (id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL,
  first_name text,
  last_name text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_id_fkey FOREIGN KEY (id) REFERENCES auth.users(id)
);
CREATE TABLE public.shipping_methods (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  code text NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT shipping_methods_pkey PRIMARY KEY (id)
);
CREATE TABLE public.tracked_products (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  blueprint_id uuid,
  print_provider_id uuid,
  printify_variant_id integer NOT NULL,
  variant_title text NOT NULL,
  size text,
  color text,
  production_cost numeric NOT NULL,
  shipping_cost numeric,
  discount_percentage integer DEFAULT 0,
  profit_margin_percentage integer DEFAULT 0,
  retail_price numeric,
  is_active boolean DEFAULT true,
  notes text,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  offers_free_shipping boolean NOT NULL DEFAULT true,
  default_shipping_country_id uuid,
  default_shipping_method_id uuid,
  CONSTRAINT tracked_products_pkey PRIMARY KEY (id),
  CONSTRAINT tracked_products_default_shipping_country_id_fkey FOREIGN KEY (default_shipping_country_id) REFERENCES public.countries(id),
  CONSTRAINT tracked_products_blueprint_id_fkey FOREIGN KEY (blueprint_id) REFERENCES public.blueprints(id),
  CONSTRAINT tracked_products_print_provider_id_fkey FOREIGN KEY (print_provider_id) REFERENCES public.print_providers(id),
  CONSTRAINT tracked_products_default_shipping_method_id_fkey FOREIGN KEY (default_shipping_method_id) REFERENCES public.shipping_methods(id)
);
CREATE TABLE public.user_api_keys (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  service_name text NOT NULL,
  api_key_encrypted text NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT user_api_keys_pkey PRIMARY KEY (id),
  CONSTRAINT user_api_keys_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.variant_shipping_costs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  tracked_product_id uuid NOT NULL,
  country_id uuid NOT NULL,
  shipping_method_id uuid NOT NULL,
  first_item_cost numeric NOT NULL DEFAULT 0,
  additional_item_cost numeric NOT NULL DEFAULT 0,
  delivery_time_min integer,
  delivery_time_max integer,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  CONSTRAINT variant_shipping_costs_pkey PRIMARY KEY (id),
  CONSTRAINT variant_shipping_costs_tracked_product_id_fkey FOREIGN KEY (tracked_product_id) REFERENCES public.tracked_products(id),
  CONSTRAINT variant_shipping_costs_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.countries(id),
  CONSTRAINT variant_shipping_costs_shipping_method_id_fkey FOREIGN KEY (shipping_method_id) REFERENCES public.shipping_methods(id)
);